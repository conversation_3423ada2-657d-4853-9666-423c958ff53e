# -*- coding: utf-8 -*-
"""
日志管理模块
负责系统日志记录和管理
"""

import logging
import logging.handlers
import os
from datetime import datetime
from typing import Optional

class LoggerManager:
    """日志管理类"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self._setup_log_directory()
        self._setup_loggers()
    
    def _setup_log_directory(self):
        """创建日志目录"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def _setup_loggers(self):
        """设置日志记录器"""
        # 主日志记录器
        self.main_logger = self._create_logger(
            "main", 
            os.path.join(self.log_dir, "main.log"),
            level=logging.INFO
        )
        
        # 串口通信日志记录器
        self.serial_logger = self._create_logger(
            "serial", 
            os.path.join(self.log_dir, "serial.log"),
            level=logging.DEBUG
        )
        
        # 测试流程日志记录器
        self.test_logger = self._create_logger(
            "test", 
            os.path.join(self.log_dir, "test.log"),
            level=logging.INFO
        )
        
        # 错误日志记录器
        self.error_logger = self._create_logger(
            "error", 
            os.path.join(self.log_dir, "error.log"),
            level=logging.ERROR
        )
    
    def _create_logger(self, name: str, log_file: str, level: int = logging.INFO) -> logging.Logger:
        """
        创建日志记录器
        
        Args:
            name: 日志记录器名称
            log_file: 日志文件路径
            level: 日志级别
            
        Returns:
            logging.Logger: 日志记录器
        """
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 文件处理器 - 支持日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # 日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def log_info(self, message: str, category: str = "main"):
        """
        记录信息日志
        
        Args:
            message: 日志消息
            category: 日志类别 (main/serial/test/error)
        """
        logger = getattr(self, f"{category}_logger", self.main_logger)
        logger.info(message)
    
    def log_debug(self, message: str, category: str = "main"):
        """
        记录调试日志
        
        Args:
            message: 日志消息
            category: 日志类别
        """
        logger = getattr(self, f"{category}_logger", self.main_logger)
        logger.debug(message)
    
    def log_warning(self, message: str, category: str = "main"):
        """
        记录警告日志
        
        Args:
            message: 日志消息
            category: 日志类别
        """
        logger = getattr(self, f"{category}_logger", self.main_logger)
        logger.warning(message)
    
    def log_error(self, message: str, category: str = "error", exception: Optional[Exception] = None):
        """
        记录错误日志
        
        Args:
            message: 日志消息
            category: 日志类别
            exception: 异常对象
        """
        logger = getattr(self, f"{category}_logger", self.error_logger)
        if exception:
            logger.error(f"{message}: {str(exception)}", exc_info=True)
        else:
            logger.error(message)
    
    def log_serial_command(self, command: bytes, direction: str = "SEND"):
        """
        记录串口指令日志
        
        Args:
            command: 指令字节数据
            direction: 方向 (SEND/RECV)
        """
        hex_str = command.hex().upper()
        formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])
        self.serial_logger.debug(f"{direction}: {formatted_hex}")
    
    def log_test_step(self, step_num: int, command_name: str, status: str, message: str = ""):
        """
        记录测试步骤日志
        
        Args:
            step_num: 步骤序号
            command_name: 指令名称
            status: 状态 (START/SUCCESS/FAILED)
            message: 附加消息
        """
        msg = f"步骤{step_num} - {command_name} - {status}"
        if message:
            msg += f" - {message}"
        self.test_logger.info(msg)
    
    def log_test_result(self, barcode: str, internal_sn: str, result: str, total_steps: int, passed_steps: int):
        """
        记录测试结果日志
        
        Args:
            barcode: 条形码
            internal_sn: 内部序列号
            result: 测试结果 (PASS/FAIL)
            total_steps: 总步骤数
            passed_steps: 通过步骤数
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        result_msg = (f"测试结果 - 时间:{timestamp}, 条形码:{barcode}, "
                     f"序列号:{internal_sn}, 结果:{result}, "
                     f"步骤:{passed_steps}/{total_steps}")
        self.test_logger.info(result_msg)
    
    def get_recent_logs(self, category: str = "main", lines: int = 100) -> list:
        """
        获取最近的日志记录
        
        Args:
            category: 日志类别
            lines: 行数
            
        Returns:
            list: 日志行列表
        """
        log_file = os.path.join(self.log_dir, f"{category}.log")
        if not os.path.exists(log_file):
            return []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
        except Exception as e:
            self.log_error(f"读取日志文件失败: {e}")
            return []
    
    def clear_logs(self, category: Optional[str] = None):
        """
        清空日志文件
        
        Args:
            category: 日志类别，为None时清空所有日志
        """
        if category:
            log_file = os.path.join(self.log_dir, f"{category}.log")
            if os.path.exists(log_file):
                open(log_file, 'w').close()
                self.log_info(f"已清空{category}日志")
        else:
            for category in ["main", "serial", "test", "error"]:
                log_file = os.path.join(self.log_dir, f"{category}.log")
                if os.path.exists(log_file):
                    open(log_file, 'w').close()
            self.log_info("已清空所有日志") 