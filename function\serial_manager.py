# -*- coding: utf-8 -*-
"""
串口管理模块
提供完善的日志记录和数据处理功能
支持多帧指令处理和优先级响应处理
"""

import serial
import time
import random
from typing import Optional, Union, List, Tuple
from datetime import datetime

class SerialManager:
    """串口通信管理类，支持完整的多帧指令处理和优先级响应"""
    
    def __init__(self, logger_manager=None):
        self.ser: Optional[serial.Serial] = None
        self.baud_rate = 9600
        self.response_interval = 0.3 # 50ms
        self.min_cmd_interval = 0.2    # 100ms 
        self.max_cmd_interval = 0.4    # 200ms
        self.logger_manager = logger_manager
        
        # 增强的缓冲区管理
        self._receive_buffer = bytearray()
        self._complete_frames = []  # 存储完整帧的队列
        
        # 统计信息
        self.stats = {
            "total_sent": 0,
            "total_received": 0,
            "bytes_sent": 0,
            "bytes_received": 0,
            "command_frames": 0,
            "response_frames": 0,
            "middle_frames": 0,
            "special_frames": 0
        }
        
    def open_port(self, port_name: str) -> bool:
        """打开指定串口"""
        try:
            self.ser = serial.Serial(
                port=port_name,
                baudrate=self.baud_rate,
                timeout=2.0,  # 接收超时2秒
                write_timeout=1.0  # 写入超时1秒
            )
            
            # 清空缓冲区
            if self.ser.in_waiting > 0:
                self.ser.read(self.ser.in_waiting)
                
            self._log_info(f"成功打开串口: {port_name}")
            return True
        except serial.SerialException as e:
            self._log_error(f"打开串口失败: {e}")
            return False
            
    def close_port(self):
        """关闭当前串口"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            self._log_info("串口已关闭")
            
    def send_command(self, data: bytes, command_type: str = "COMMAND", 
                    is_response: bool = False) -> bool:
        """
        发送指令并记录详细日志
        :param data: 要发送的字节数据
        :param command_type: 指令类型标识
        :param is_response: 是否为响应帧(使用50ms间隔)
        :return: 是否发送成功
        """
        if not self.ser or not self.ser.is_open:
            self._log_error("串口未打开，无法发送指令")
            return False
            
        try:
            # 计算并等待适当间隔
            wait_time = (self.response_interval if is_response else
                        random.uniform(self.min_cmd_interval, self.max_cmd_interval))
            time.sleep(wait_time)
            
            # 记录发送前的状态
            self._log_debug(f"准备发送 {command_type}，等待时间: {wait_time:.3f}s")
            
            # 发送数据
            sent_bytes = self.ser.write(data)
            
            # 确保数据被发送
            self.ser.flush()
            
            # 更新统计
            self.stats["total_sent"] += 1
            self.stats["bytes_sent"] += sent_bytes
            
            # 根据类型更新具体统计
            if is_response:
                if "RESPONSE" in command_type:
                    self.stats["response_frames"] += 1
                elif "MIDDLE" in command_type:
                    self.stats["middle_frames"] += 1
            else:
                self.stats["command_frames"] += 1
            
            # 记录详细日志
            self._log_serial_command(data, "SEND", command_type, wait_time)
            
            # 验证发送的字节数
            if sent_bytes != len(data):
                self._log_warning(f"发送字节数不匹配: 期望 {len(data)}, 实际 {sent_bytes}")
                return False
            
            return True
        except serial.SerialException as e:
            self._log_error(f"发送指令失败: {e}")
            return False
            
    def receive_data_enhanced(self, timeout: float = 2.0, 
                            expected_frames: int = 1) -> List[bytes]:
        """
        增强的数据接收方法，支持接收多个完整帧
        :param timeout: 超时时间(秒)
        :param expected_frames: 期望接收的帧数
        :return: 接收到的完整帧列表
        """
        if not self.ser or not self.ser.is_open:
            self._log_error("串口未打开，无法接收数据")
            return []
        
        # 先返回已经缓存的完整帧
        if self._complete_frames:
            frames = self._complete_frames[:expected_frames]
            self._complete_frames = self._complete_frames[expected_frames:]
            return frames
            
        start_time = time.time()
        received_frames = []
        
        while (time.time() - start_time) < timeout and len(received_frames) < expected_frames:
            # 检查是否有新数据
            if self.ser.in_waiting > 0:
                new_data = self.ser.read(self.ser.in_waiting)
                self._receive_buffer.extend(new_data)
                self.stats["bytes_received"] += len(new_data)
                
                self._log_debug(f"接收到 {len(new_data)} 字节原始数据: {new_data.hex().upper()}")
            
            # 尝试从缓冲区提取完整帧
            while True:
                frame = self._extract_complete_frame()
                if frame is None:
                    break
                    
                received_frames.append(frame)
                self.stats["total_received"] += 1
                
                # 记录接收日志
                frame_type = self._identify_frame_type(frame)
                self._log_serial_command(frame, "RECV", frame_type)
                
                if len(received_frames) >= expected_frames:
                    break
            
            # 如果还没收到足够的帧，短暂等待
            if len(received_frames) < expected_frames:
                time.sleep(0.01)  # 10ms
        
        return received_frames
        
    def receive_single_frame(self, timeout: float = 2.0) -> Optional[bytes]:
        """接收单个完整帧"""
        start_time = time.time()
        
        # 首先检查是否有已缓冲的完整帧
        if self._complete_frames:
            frame = self._complete_frames.pop(0)
            frame_type = self._identify_frame_type(frame)
            self._log_serial_command(frame, "RECV", frame_type)
            self.stats["total_received"] += 1
            return frame
        
        while (time.time() - start_time) < timeout:
            # 检查是否有新数据
            if self.ser and self.ser.is_open and self.ser.in_waiting > 0:
                new_data = self.ser.read(self.ser.in_waiting)
                self._receive_buffer.extend(new_data)
                self.stats["bytes_received"] += len(new_data)
                
                self._log_debug(f"接收到 {len(new_data)} 字节原始数据: {new_data.hex().upper()}")
            
            # 尝试从缓冲区提取完整帧
            frame = self._extract_complete_frame()
            if frame is not None:
                self.stats["total_received"] += 1
                
                # 记录接收日志
                frame_type = self._identify_frame_type(frame)
                self._log_serial_command(frame, "RECV", frame_type)
                
                return frame
            
            # 短暂等待
            time.sleep(0.001)  # 1ms
        
        # 超时，检查缓冲区是否有不完整的数据
        if self._receive_buffer:
            self._log_warning(f"接收超时，缓冲区剩余数据: {self._receive_buffer.hex().upper()}")
        
        return None
        
    def _extract_complete_frame(self) -> Optional[bytes]:
        """从接收缓冲区提取一个完整帧，支持二进制帧和ASCII帧"""
        if len(self._receive_buffer) < 2:
            return None

        # 首先尝试提取ASCII帧（以\r\n结尾）
        ascii_frame = self._extract_ascii_frame()
        if ascii_frame:
            return ascii_frame

        # 然后尝试提取二进制帧（以0x7E开头和结尾）
        return self._extract_binary_frame()

    def _extract_ascii_frame(self) -> Optional[bytes]:
        """提取ASCII格式的帧（以\r\n结尾）"""
        line_ending = b'\r\n'

        # 查找换行符
        end_idx = self._receive_buffer.find(line_ending)
        if end_idx == -1:
            return None

        # 提取完整的ASCII帧（包括换行符）
        frame = bytes(self._receive_buffer[:end_idx + len(line_ending)])
        self._receive_buffer = self._receive_buffer[end_idx + len(line_ending):]

        return frame

    def _extract_binary_frame(self) -> Optional[bytes]:
        """提取二进制格式的帧（以0x7E开头和结尾）"""
        # 查找帧头 0x7E
        start_idx = -1
        for i in range(len(self._receive_buffer)):
            if self._receive_buffer[i] == 0x7E:
                start_idx = i
                break

        if start_idx == -1:
            # 没有找到帧头，但不清空缓冲区（可能有ASCII数据）
            return None

        # 如果帧头不在开始位置，移除前面的无效数据
        if start_idx > 0:
            invalid_data = self._receive_buffer[:start_idx]
            self._log_warning(f"丢弃无效数据: {invalid_data.hex().upper()}")
            self._receive_buffer = self._receive_buffer[start_idx:]

        # 查找帧尾 0x7E（从第二个字节开始查找）
        if len(self._receive_buffer) < 2:
            return None

        end_idx = -1
        for i in range(1, len(self._receive_buffer)):
            if self._receive_buffer[i] == 0x7E:
                end_idx = i
                break

        if end_idx == -1:
            # 没有找到帧尾，等待更多数据
            return None

        # 提取完整帧
        frame = bytes(self._receive_buffer[:end_idx + 1])
        self._receive_buffer = self._receive_buffer[end_idx + 1:]

        return frame
        
    def _identify_frame_type(self, frame: bytes) -> str:
        """识别帧类型，支持二进制帧和ASCII帧"""
        # 首先检查是否是ASCII帧
        if frame.endswith(b'\r\n'):
            return self._identify_ascii_frame_type(frame)

        # 然后检查二进制帧
        if frame == bytes.fromhex("7E 01 93 8D B0 7E"):
            return "RESPONSE_FRAME"
        elif frame == bytes.fromhex("7E 01 73 83 57 7E"):
            return "RESPONSE_ECHO"
        elif frame == bytes.fromhex("7E 01 11 97 17 7E"):
            return "MIDDLE_FRAME"
        elif frame == bytes.fromhex("7E 01 31 95 36 7E"):
            return "SPECIAL_FRAME"
        elif len(frame) >= 4 and frame[1:4] == bytes.fromhex("01 30"):
            # 进一步细分01 30开头的响应类型
            if len(frame) >= 10:
                cmd_bytes = frame[6:10]  # 提取指令字节
                if cmd_bytes == bytes.fromhex("48 57 10 00"):
                    return "WRITE_TAG_RESPONSE"  # 写电子标签响应
                elif cmd_bytes == bytes.fromhex("48 57 11 00"):
                    return "READ_TAG_RESPONSE"   # 读电子标签响应
                elif cmd_bytes == bytes.fromhex("48 57 86 00"):
                    return "WRITE_FILE_RESPONSE" # 写文件响应
                elif cmd_bytes == bytes.fromhex("48 57 87 00"):
                    return "READ_FILE_RESPONSE"  # 读文件响应
                elif cmd_bytes == bytes.fromhex("48 57 88 00"):
                    return "WRITE_TEMP_RESPONSE" # 写温度响应
                elif cmd_bytes == bytes.fromhex("48 57 89 00"):
                    return "READ_TEMP_RESPONSE"  # 读温度响应
                elif cmd_bytes == bytes.fromhex("48 57 80 00"):
                    return "READ_SN_RESPONSE"    # 读序列号响应
                elif cmd_bytes == bytes.fromhex("48 57 81 00"):
                    return "WRITE_SN_RESPONSE"   # 写序列号响应
                elif cmd_bytes == bytes.fromhex("48 57 90 00"):
                    return "READ_SPEED_RESPONSE" # 读转速响应
                elif cmd_bytes == bytes.fromhex("48 57 EA 00"):
                    return "BACKUP_SN_RESPONSE"  # 备份序列号响应
                elif cmd_bytes == bytes.fromhex("48 57 56 00"):
                    return "FLASH_CHECK_RESPONSE" # Flash自检响应
                elif cmd_bytes == bytes.fromhex("48 57 50 00"):
                    return "EQUIP_MODE_RESPONSE"  # 装备模式响应
            return "NORMAL_RESPONSE"
        elif len(frame) >= 4 and frame[1:4] == bytes.fromhex("00 BB"):
            return "BROADCAST_RESPONSE"
        elif len(frame) >= 4 and frame[1:4] == bytes.fromhex("01 BF"):
            return "ADDRESS_RESPONSE"
        else:
            return "UNKNOWN"

    def _identify_ascii_frame_type(self, frame: bytes) -> str:
        """识别ASCII帧类型"""
        try:
            # 移除换行符并转换为字符串
            content = frame.replace(b'\r\n', b'').decode('ascii', errors='ignore')

            # 根据内容识别帧类型
            if content.startswith('*IDN') or ('AIMM' in content and 'v' in content):
                return "POWER_DEVICE_INFO_RESPONSE"
            elif content.endswith('V'):
                return "POWER_VOLTAGE_RESPONSE"
            elif content.endswith('A'):
                return "POWER_CURRENT_RESPONSE"
            elif len(content) == 8 and all(c in '01' for c in content):
                return "POWER_STATUS_BINARY_RESPONSE"
            elif content in ['REMOTE', 'OUT0', 'OUT1', 'STATUS']:
                return "POWER_COMMAND_ECHO"
            elif content.startswith('VSET') or content.startswith('ISET'):
                return "POWER_SETTING_ECHO"
            else:
                return "POWER_UNKNOWN_RESPONSE"
        except:
            return "ASCII_UNKNOWN"

    def handle_special_frames_enhanced(self, max_retries: int = 100) -> Tuple[bool, List[bytes]]:
        """
        增强的特殊帧处理，返回所有接收到的数据
        :param max_retries: 最大重试次数（默认提高到100次）
        :return: (是否成功, 接收到的所有帧列表)
        """
        SPECIAL_FRAME = bytes.fromhex("7E 01 31 95 36 7E")
        MIDDLE_FRAME = bytes.fromhex("7E 01 11 97 17 7E")
        
        all_received_frames = []
        retry_count = 0
        
        while retry_count < max_retries:
            # 检查是否有待处理的数据
            frames = self.receive_data_enhanced(timeout=0.1, expected_frames=1)
            
            if not frames:
                # 没有收到数据，结束处理
                break
                
            frame = frames[0]
            all_received_frames.append(frame)
            
            if frame == SPECIAL_FRAME:
                # 收到特殊帧，发送中间帧
                self._log_info(f"收到第 {retry_count + 1} 个特殊帧，发送中间帧响应")
                if not self.send_command(MIDDLE_FRAME, "MIDDLE", is_response=True):
                    return False, all_received_frames
                retry_count += 1
                continue
            else:
                # 收到其他数据，处理完成
                self._log_info(f"收到非特殊帧数据，特殊帧处理完成: {frame.hex().upper()}")
                break
                
        if retry_count >= max_retries:
            self._log_warning(f"特殊帧处理达到最大重试次数 {max_retries}，强制结束")
                
        return True, all_received_frames
        
    def get_statistics(self) -> dict:
        """获取统计信息"""
        return self.stats.copy()
        
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "total_sent": 0,
            "total_received": 0,
            "bytes_sent": 0,
            "bytes_received": 0,
            "command_frames": 0,
            "response_frames": 0,
            "middle_frames": 0,
            "special_frames": 0
        }
        
    def clear_buffers(self):
        """清空所有缓冲区"""
        self._receive_buffer.clear()
        self._complete_frames.clear()
        if self.ser and self.ser.is_open and self.ser.in_waiting > 0:
            discarded = self.ser.read(self.ser.in_waiting)
            self._log_debug(f"清空串口缓冲区，丢弃 {len(discarded)} 字节")
    
    def _log_serial_command(self, command: bytes, direction: str,
                          command_type: str = "", wait_time: float = 0.0):
        """记录串口指令的详细日志，电源指令以ASCII格式显示"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]

        # 检查是否是ASCII帧（电源指令）
        is_ascii_frame = self.is_ascii_frame(command)

        if is_ascii_frame:
            # 电源指令以ASCII格式显示
            ascii_content = self.get_ascii_content(command)
            hex_str = command.hex().upper()
            formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])

            if direction == "SEND":
                log_msg = f"[{timestamp}] SEND {command_type} (电源指令): ASCII='{ascii_content}' HEX={formatted_hex}"
                if wait_time > 0:
                    log_msg += f" (等待: {wait_time:.3f}s)"
                log_msg += f" [已发送 {len(command)} 字节]"
            else:
                log_msg = f"[{timestamp}] RECV {command_type} (电源响应): ASCII='{ascii_content}' HEX={formatted_hex}"
                log_msg += f" [接收到 {len(command)} 字节]"
        else:
            # 二进制指令以十六进制格式显示
            hex_str = command.hex().upper()
            formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])

            if direction == "SEND":
                log_msg = f"[{timestamp}] SEND {command_type}: {formatted_hex}"
                if wait_time > 0:
                    log_msg += f" (等待: {wait_time:.3f}s)"
                log_msg += f" [已发送 {len(command)} 字节]"
            else:
                log_msg = f"[{timestamp}] RECV {command_type}: {formatted_hex}"
                log_msg += f" [接收到 {len(command)} 字节]"

        # 同时记录到调试日志和信息日志，确保可见性
        self._log_info(log_msg, category="serial")

        # 如果是响应帧相关的重要操作，额外强调
        if "RESPONSE" in command_type or "ECHO" in command_type:
            self._log_info(f"*** 响应帧操作 *** {log_msg}", category="serial")

        # 如果是电源指令，额外强调ASCII内容
        if is_ascii_frame:
            self._log_info(f"*** 电源指令ASCII内容 *** '{ascii_content}'", category="serial")
        
    def _log_info(self, message: str, category: str = "serial"):
        """记录信息日志"""
        if self.logger_manager:
            self.logger_manager.log_info(message, category)
        else:
            print(f"INFO: {message}")
            
    def _log_debug(self, message: str, category: str = "serial"):
        """记录调试日志"""
        if self.logger_manager:
            self.logger_manager.log_debug(message, category)
        else:
            print(f"DEBUG: {message}")
            
    def _log_warning(self, message: str, category: str = "serial"):
        """记录警告日志"""
        if self.logger_manager:
            self.logger_manager.log_warning(message, category)
        else:
            print(f"WARNING: {message}")
            
    def _log_error(self, message: str, category: str = "error"):
        """记录错误日志"""
        if self.logger_manager:
            self.logger_manager.log_error(message, category)
        else:
            print(f"ERROR: {message}")

    def _log_debug(self, message: str):
        """记录调试日志"""
        if self.logger_manager:
            self.logger_manager.log_debug(message, "serial")
        else:
            print(f"[SERIAL DEBUG] {message}")

    def send_power_command(self, data: bytes, command_type: str = "POWER_COMMAND") -> bool:
        """
        发送电源指令（ASCII格式）
        :param data: ASCII指令数据（已包含换行符）
        :param command_type: 指令类型标识
        :return: 是否发送成功
        """
        return self.send_command(data, command_type, is_response=False)

    def receive_power_response(self, timeout: float = 2.0) -> Optional[bytes]:
        """
        接收电源指令响应（ASCII格式）
        :param timeout: 超时时间
        :return: 接收到的ASCII响应数据
        """
        return self.receive_single_frame(timeout)

    def send_power_command_sequence(self, commands: List[bytes], timeout_per_command: float = 1.0) -> List[Optional[bytes]]:
        """
        发送电源指令序列并接收每个指令的响应
        :param commands: 指令列表
        :param timeout_per_command: 每个指令的超时时间
        :return: 响应列表
        """
        responses = []

        for i, cmd in enumerate(commands):
            self._log_info(f"发送电源指令序列 {i+1}/{len(commands)}")

            # 发送指令
            if self.send_power_command(cmd, f"POWER_SEQUENCE_{i+1}"):
                # 电源指令发送后不等待响应，避免超时问题
                # 简单等待一小段时间后继续
                import time
                time.sleep(0.05)  # 等待50ms后继续

                responses.append(None)  # 不接收响应，直接标记为None
                self._log_debug(f"电源指令 {i+1} 发送完成，等待50ms后继续")
            else:
                self._log_error(f"发送电源指令 {i+1} 失败")
                responses.append(None)

        return responses

    def send_power_command_sequence_with_interval(self, commands: List[bytes], interval_ms: int = 10, timeout_per_command: float = 0.1) -> List[Optional[bytes]]:
        """
        发送电源指令序列，每个指令间有指定的时间间隔
        :param commands: 指令列表
        :param interval_ms: 指令间间隔时间（毫秒），默认10ms
        :param timeout_per_command: 每个指令的超时时间，默认0.1s
        :return: 响应列表
        """
        import time

        responses = []
        interval_seconds = interval_ms / 1000.0  # 转换为秒

        self._log_info(f"发送电源指令序列，共{len(commands)}个指令，间隔{interval_ms}ms，超时{timeout_per_command}s")

        for i, cmd in enumerate(commands):
            command_str = cmd.decode('ascii', errors='ignore').strip()
            self._log_debug(f"发送电源指令 {i+1}/{len(commands)}: {command_str}")

            # 发送指令
            if self.send_power_command(cmd, f"POWER_INTERVAL_{i+1}"):
                # 电源指令发送后不等待响应，直接继续
                # 只是简单等待指定的超时时间，然后继续下一个指令
                import time
                time.sleep(timeout_per_command)

                # 不接收响应，直接标记为None
                responses.append(None)
                self._log_debug(f"电源指令 {i+1} 发送完成，等待{timeout_per_command}s后继续")
            else:
                self._log_error(f"发送电源指令 {i+1} 失败")
                responses.append(None)

            # 如果不是最后一个指令，添加间隔
            if i < len(commands) - 1:
                self._log_debug(f"等待{interval_ms}ms后发送下一个指令")
                time.sleep(interval_seconds)

        self._log_info(f"电源指令序列发送完成")
        return responses

    def is_ascii_frame(self, frame: bytes) -> bool:
        """判断是否是ASCII帧"""
        return frame.endswith(b'\r\n')

    def get_ascii_content(self, frame: bytes) -> str:
        """从ASCII帧中提取内容"""
        try:
            return frame.replace(b'\r\n', b'').decode('ascii', errors='ignore')
        except:
            return ""