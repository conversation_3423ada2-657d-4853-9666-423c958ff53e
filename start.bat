@echo off
chcp 65001 > nul
title 上位机自动化测试系统

echo 正在启动上位机自动化测试系统...
echo.

REM 检查Python是否安装
python --version > nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python环境，请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

REM 检查依赖包
echo 检查依赖包...
pip show PyQt5 > nul 2>&1
if errorlevel 1 (
    echo 正在安装PyQt5...
    pip install PyQt5
)

pip show pyserial > nul 2>&1
if errorlevel 1 (
    echo 正在安装pyserial...
    pip install pyserial
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "config" mkdir config
if not exist "backups" mkdir backups

echo.
echo 启动程序...
python main.py

if errorlevel 1 (
    echo.
    echo 程序异常退出，请查看日志文件获取更多信息
    pause
)

pause 