# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file '1.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1280, 720)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.widget = QtWidgets.QWidget(self.centralwidget)
        self.widget.setObjectName("widget")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.widget)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.widgetLeft = QtWidgets.QWidget(self.widget)
        self.widgetLeft.setObjectName("widgetLeft")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.widgetLeft)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.labelWelcome = QtWidgets.QLabel(self.widgetLeft)
        font = QtGui.QFont()
        font.setPointSize(25)
        self.labelWelcome.setFont(font)
        self.labelWelcome.setObjectName("labelWelcome")
        self.verticalLayout_2.addWidget(self.labelWelcome)
        self.widgetLeftButtom = QtWidgets.QWidget(self.widgetLeft)
        self.widgetLeftButtom.setObjectName("widgetLeftButtom")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.widgetLeftButtom)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.calendarWidget = QtWidgets.QCalendarWidget(self.widgetLeftButtom)
        self.calendarWidget.setObjectName("calendarWidget")
        self.verticalLayout_5.addWidget(self.calendarWidget)
        self.widgetOption = QtWidgets.QWidget(self.widgetLeftButtom)
        self.widgetOption.setObjectName("widgetOption")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.widgetOption)
        self.gridLayout_3.setHorizontalSpacing(20)
        self.gridLayout_3.setVerticalSpacing(15)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.pushButtonMES = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonMES.setFont(font)
        self.pushButtonMES.setObjectName("pushButtonMES")
        self.gridLayout_3.addWidget(self.pushButtonMES, 0, 0, 1, 1)
        self.pushButtonSystem = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonSystem.setFont(font)
        self.pushButtonSystem.setObjectName("pushButtonSystem")
        self.gridLayout_3.addWidget(self.pushButtonSystem, 0, 1, 1, 1)
        self.pushButtonProgram = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonProgram.setFont(font)
        self.pushButtonProgram.setObjectName("pushButtonProgram")
        self.gridLayout_3.addWidget(self.pushButtonProgram, 1, 0, 1, 1)
        self.pushButtonPermission = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonPermission.setFont(font)
        self.pushButtonPermission.setObjectName("pushButtonPermission")
        self.gridLayout_3.addWidget(self.pushButtonPermission, 1, 1, 1, 1)
        self.pushButtonHardware = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonHardware.setFont(font)
        self.pushButtonHardware.setObjectName("pushButtonHardware")
        self.gridLayout_3.addWidget(self.pushButtonHardware, 2, 0, 1, 1)
        self.pushButtonProduct = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonProduct.setFont(font)
        self.pushButtonProduct.setObjectName("pushButtonProduct")
        self.gridLayout_3.addWidget(self.pushButtonProduct, 2, 1, 1, 1)
        self.pushButtonPassword = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonPassword.setFont(font)
        self.pushButtonPassword.setObjectName("pushButtonPassword")
        self.gridLayout_3.addWidget(self.pushButtonPassword, 3, 0, 1, 1)
        self.pushButtonExit = QtWidgets.QPushButton(self.widgetOption)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonExit.setFont(font)
        self.pushButtonExit.setObjectName("pushButtonExit")
        self.gridLayout_3.addWidget(self.pushButtonExit, 3, 1, 1, 1)
        self.verticalLayout_5.addWidget(self.widgetOption)
        self.verticalLayout_5.setStretch(0, 1)
        self.verticalLayout_5.setStretch(1, 5)
        self.verticalLayout_2.addWidget(self.widgetLeftButtom)
        self.verticalLayout_2.setStretch(0, 1)
        self.verticalLayout_2.setStretch(1, 5)
        self.horizontalLayout.addWidget(self.widgetLeft)
        self.stackedWidget_2 = QtWidgets.QStackedWidget(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.stackedWidget_2.sizePolicy().hasHeightForWidth())
        self.stackedWidget_2.setSizePolicy(sizePolicy)
        self.stackedWidget_2.setMaximumSize(QtCore.QSize(1677721, 1677721))
        self.stackedWidget_2.setStyleSheet("""
            QWidget {
                background: #FAFAFA;
            }
            QPushButton {
                background-color: #2979ff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
            QPushButton:disabled {
                background-color: #bdbdbd;
                color: #757575;
            }
            QToolButton {
                background-color: #bfbfbf;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 4px;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: #1565c0;
            }
        """)
        self.stackedWidget_2.setObjectName("stackedWidget_2")
        self.pageTest = QtWidgets.QWidget()
        self.pageTest.setObjectName("pageTest")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.pageTest)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.widgetEditSelection = QtWidgets.QWidget(self.pageTest)
        self.widgetEditSelection.setObjectName("widgetEditSelection")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.widgetEditSelection)
        self.horizontalLayout_8.setContentsMargins(10, 20, 10, 20)
        self.horizontalLayout_8.setSpacing(100)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.pushButtonLoadProgram = QtWidgets.QPushButton(self.widgetEditSelection)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonLoadProgram.sizePolicy().hasHeightForWidth())
        self.pushButtonLoadProgram.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonLoadProgram.setFont(font)
        self.pushButtonLoadProgram.setObjectName("pushButtonLoadProgram")
        self.horizontalLayout_8.addWidget(self.pushButtonLoadProgram)
        self.pushButtonStartTest = QtWidgets.QPushButton(self.widgetEditSelection)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonStartTest.sizePolicy().hasHeightForWidth())
        self.pushButtonStartTest.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonStartTest.setFont(font)
        self.pushButtonStartTest.setObjectName("pushButtonStartTest")
        self.horizontalLayout_8.addWidget(self.pushButtonStartTest)
        self.pushButtonExitSystem = QtWidgets.QPushButton(self.widgetEditSelection)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonExitSystem.sizePolicy().hasHeightForWidth())
        self.pushButtonExitSystem.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.pushButtonExitSystem.setFont(font)
        self.pushButtonExitSystem.setObjectName("pushButtonExitSystem")
        self.horizontalLayout_8.addWidget(self.pushButtonExitSystem)
        self.verticalLayout_3.addWidget(self.widgetEditSelection)
        self.widgetShowTable = QtWidgets.QTableWidget(self.pageTest)
        self.widgetShowTable.setObjectName("widgetShowTable")
        self.widgetShowTable.setColumnCount(7)
        self.widgetShowTable.setRowCount(1)
        self.widgetShowTable.verticalHeader().hide()
        self.widgetShowTable.setHorizontalHeaderLabels([
            "序号", "COM口", "名称", "内容", "规格", "文件类型", "文件路径"
        ])
        self.widgetShowTable.horizontalHeader().setDefaultAlignment(QtCore.Qt.AlignLeft)
        self.widgetShowTable.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.verticalLayout_3.addWidget(self.widgetShowTable)
        self.tabWidgetResult = QtWidgets.QTabWidget(self.pageTest)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.tabWidgetResult.setFont(font)
        self.tabWidgetResult.setObjectName("tabWidgetResult")
        self.tabTestData = QtWidgets.QWidget()
        self.tabTestData.setObjectName("tabTestData")
        self.gridLayout = QtWidgets.QGridLayout(self.tabTestData)
        self.gridLayout.setObjectName("gridLayout")
        self.textEditBackData = QtWidgets.QTextEdit(self.tabTestData)
        self.textEditBackData.setReadOnly(True)
        self.textEditBackData.setObjectName("textEditBackData")
        self.gridLayout.addWidget(self.textEditBackData, 1, 0, 1, 1)
        self.textEditResultData = QtWidgets.QTextEdit(self.tabTestData)
        self.textEditResultData.setReadOnly(True)
        self.textEditResultData.setObjectName("textEditResultData")
        self.gridLayout.addWidget(self.textEditResultData, 1, 1, 1, 1)
        self.textEditShowInfo = QtWidgets.QTextEdit(self.tabTestData)
        self.textEditShowInfo.setReadOnly(True)
        self.textEditShowInfo.setObjectName("textEditShowInfo")
        self.gridLayout.addWidget(self.textEditShowInfo, 0, 1, 1, 1)
        self.labelShow = QtWidgets.QLabel(self.tabTestData)
        self.labelShow.setText("")
        self.labelShow.setObjectName("labelShow")
        self.labelShow.setStyleSheet("""
            QLabel#labelShow {
                border: 1px solid #000;
                background-color: white;
                padding: 5px;
            }
        """)
        self.gridLayout.addWidget(self.labelShow, 0, 0, 1, 1)
        self.gridLayout.setRowStretch(0, 2)
        self.gridLayout.setRowStretch(1, 3)
        self.tabWidgetResult.addTab(self.tabTestData, "")
        self.tabData = QtWidgets.QWidget()
        self.tabData.setObjectName("tabData")
        self.tabWidgetResult.addTab(self.tabData, "")
        self.verticalLayout_3.addWidget(self.tabWidgetResult)
        self.verticalLayout_3.setStretch(0, 1)
        self.verticalLayout_3.setStretch(1, 3)
        self.verticalLayout_3.setStretch(2, 5)
        self.stackedWidget_2.addWidget(self.pageTest)
        self.pageSet = QtWidgets.QWidget()
        self.pageSet.setObjectName("pageSet")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.pageSet)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.widgetShow = QtWidgets.QWidget(self.pageSet)
        self.widgetShow.setObjectName("widgetShow")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.widgetShow)
        self.gridLayout_2.setContentsMargins(15, 9, 15, 9)
        self.gridLayout_2.setHorizontalSpacing(50)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.labelShowName = QtWidgets.QLabel(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelShowName.setFont(font)
        self.labelShowName.setObjectName("labelShowName")
        self.gridLayout_2.addWidget(self.labelShowName, 0, 0, 1, 1)
        self.lineEditShowName = QtWidgets.QLineEdit(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.lineEditShowName.setFont(font)
        self.lineEditShowName.setObjectName("lineEditShowName")
        self.gridLayout_2.addWidget(self.lineEditShowName, 0, 1, 1, 1)
        self.labelShowText = QtWidgets.QLabel(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelShowText.setFont(font)
        self.labelShowText.setObjectName("labelShowText")
        self.gridLayout_2.addWidget(self.labelShowText, 0, 2, 1, 1)
        self.lineEditShowText = QtWidgets.QLineEdit(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.lineEditShowText.setFont(font)
        self.lineEditShowText.setReadOnly(False)
        self.lineEditShowText.setObjectName("lineEditShowText")
        self.gridLayout_2.addWidget(self.lineEditShowText, 0, 3, 1, 2)
        self.labelShowCOM = QtWidgets.QLabel(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelShowCOM.setFont(font)
        self.labelShowCOM.setObjectName("labelShowCOM")
        self.gridLayout_2.addWidget(self.labelShowCOM, 1, 0, 1, 1)
        self.comboBoxShowCOM = QtWidgets.QComboBox(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.comboBoxShowCOM.setFont(font)
        self.comboBoxShowCOM.setObjectName("comboBoxShowCOM")
        self.gridLayout_2.addWidget(self.comboBoxShowCOM, 1, 1, 1, 1)
        self.labelShowFormat = QtWidgets.QLabel(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelShowFormat.setFont(font)
        self.labelShowFormat.setObjectName("labelShowFormat")
        self.gridLayout_2.addWidget(self.labelShowFormat, 1, 2, 1, 1)
        self.lineEditShowFormat = QtWidgets.QLineEdit(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.lineEditShowFormat.setFont(font)
        self.lineEditShowFormat.setObjectName("lineEditShowFormat")
        self.gridLayout_2.addWidget(self.lineEditShowFormat, 1, 3, 1, 2)
        self.labelShowFileType = QtWidgets.QLabel(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelShowFileType.setFont(font)
        self.labelShowFileType.setObjectName("labelShowFileType")
        self.gridLayout_2.addWidget(self.labelShowFileType, 2, 0, 1, 1)
        self.comboBoxShowFileType = QtWidgets.QComboBox(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.comboBoxShowFileType.setFont(font)
        self.comboBoxShowFileType.setObjectName("comboBoxShowFileType")
        self.gridLayout_2.addWidget(self.comboBoxShowFileType, 2, 1, 1, 1)
        self.labelShowFile = QtWidgets.QLabel(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelShowFile.setFont(font)
        self.labelShowFile.setObjectName("labelShowFile")
        self.gridLayout_2.addWidget(self.labelShowFile, 2, 2, 1, 1)
        self.lineEditShowFile = QtWidgets.QLineEdit(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.lineEditShowFile.setFont(font)
        self.lineEditShowFile.setObjectName("lineEditShowFile")
        self.gridLayout_2.addWidget(self.lineEditShowFile, 2, 3, 1, 1)
        self.toolButtonShowFile = QtWidgets.QToolButton(self.widgetShow)
        self.toolButtonShowFile.setObjectName("toolButtonShowFile")
        self.gridLayout_2.addWidget(self.toolButtonShowFile, 2, 4, 1, 1)
        self.labelShowCode = QtWidgets.QLabel(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelShowCode.setFont(font)
        self.labelShowCode.setObjectName("labelShowCode")
        self.gridLayout_2.addWidget(self.labelShowCode, 3, 0, 1, 1)
        self.comboBoxShowCode = QtWidgets.QComboBox(self.widgetShow)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.comboBoxShowCode.setFont(font)
        self.comboBoxShowCode.setObjectName("comboBoxShowCode")
        self.gridLayout_2.addWidget(self.comboBoxShowCode, 3, 1, 1, 1)
        self.verticalLayout_4.addWidget(self.widgetShow)
        self.widget_2 = QtWidgets.QWidget(self.pageSet)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.widget_2.setFont(font)
        self.widget_2.setObjectName("widget_2")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.widget_2)
        self.horizontalLayout_2.setSpacing(10)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.pushButtonMoveItem = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButtonMoveItem.setFont(font)
        self.pushButtonMoveItem.setObjectName("pushButtonMoveItem")
        self.horizontalLayout_2.addWidget(self.pushButtonMoveItem)
        self.pushButtonMoveFront = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButtonMoveFront.setFont(font)
        self.pushButtonMoveFront.setObjectName("pushButtonMoveFront")
        self.horizontalLayout_2.addWidget(self.pushButtonMoveFront)
        self.pushButton_MoveBehind = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButton_MoveBehind.setFont(font)
        self.pushButton_MoveBehind.setObjectName("pushButton_MoveBehind")
        self.horizontalLayout_2.addWidget(self.pushButton_MoveBehind)
        self.pushButtonAddItem = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButtonAddItem.setFont(font)
        self.pushButtonAddItem.setObjectName("pushButtonAddItem")
        self.horizontalLayout_2.addWidget(self.pushButtonAddItem)
        self.pushButtonDeleteItem = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButtonDeleteItem.setFont(font)
        self.pushButtonDeleteItem.setObjectName("pushButtonDeleteItem")
        self.horizontalLayout_2.addWidget(self.pushButtonDeleteItem)
        self.pushButtonOpenFile = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButtonOpenFile.setFont(font)
        self.pushButtonOpenFile.setObjectName("pushButtonOpenFile")
        self.horizontalLayout_2.addWidget(self.pushButtonOpenFile)
        self.pushButtonSaveFile = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButtonSaveFile.setFont(font)
        self.pushButtonSaveFile.setObjectName("pushButtonSaveFile")
        self.horizontalLayout_2.addWidget(self.pushButtonSaveFile)
        self.pushButtonExitSys = QtWidgets.QPushButton(self.widget_2)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.pushButtonExitSys.setFont(font)
        self.pushButtonExitSys.setObjectName("pushButtonExitSys")
        self.horizontalLayout_2.addWidget(self.pushButtonExitSys)
        self.verticalLayout_4.addWidget(self.widget_2)
        self.widgetCMD = QtWidgets.QWidget(self.pageSet)
        self.widgetCMD.setObjectName("widgetCMD")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.widgetCMD)
        self.horizontalLayout_3.setSpacing(20)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.treeCMD = QtWidgets.QTreeWidget(self.widgetCMD)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.treeCMD.sizePolicy().hasHeightForWidth())
        self.treeCMD.setSizePolicy(sizePolicy)
        self.treeCMD.setObjectName("treeCMD")
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        item_0 = QtWidgets.QTreeWidgetItem(self.treeCMD)
        self.horizontalLayout_3.addWidget(self.treeCMD)
        self.tableWidget_2 = QtWidgets.QTableWidget(self.widgetCMD)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tableWidget_2.sizePolicy().hasHeightForWidth())
        self.tableWidget_2.setSizePolicy(sizePolicy)
        self.tableWidget_2.setMaximumSize(QtCore.QSize(16777215, 1677721))
        self.tableWidget_2.setSizeIncrement(QtCore.QSize(1, 1))
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setKerning(True)
        self.tableWidget_2.setFont(font)
        self.tableWidget_2.setObjectName("tableWidget_2")
        self.tableWidget_2.setColumnCount(7)
        self.tableWidget_2.setRowCount(2)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(0, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(0, 5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(0, 6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(1, 0, item)
        self.horizontalLayout_3.addWidget(self.tableWidget_2)
        self.horizontalLayout_3.setStretch(0, 3)
        self.horizontalLayout_3.setStretch(1, 7)
        self.verticalLayout_4.addWidget(self.widgetCMD)
        self.verticalLayout_4.setStretch(0, 4)
        self.verticalLayout_4.setStretch(1, 1)
        self.verticalLayout_4.setStretch(2, 9)
        self.stackedWidget_2.addWidget(self.pageSet)
        self.pagePermission = QtWidgets.QWidget()
        self.pagePermission.setObjectName("pagePermission")
        self.label_21 = QtWidgets.QLabel(self.pagePermission)
        self.label_21.setGeometry(QtCore.QRect(170, 20, 271, 71))
        font = QtGui.QFont()
        font.setPointSize(35)
        self.label_21.setFont(font)
        self.label_21.setObjectName("label_21")
        self.stackedWidget_2.addWidget(self.pagePermission)
        self.page = QtWidgets.QWidget()
        self.page.setObjectName("page")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.page)
        self.horizontalLayout_7.setContentsMargins(50, 200, 50, 200)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.label_14 = QtWidgets.QLabel(self.page)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_14.sizePolicy().hasHeightForWidth())
        self.label_14.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        self.label_14.setFont(font)
        self.label_14.setText("")
        self.label_14.setScaledContents(True)
        self.label_14.setAlignment(QtCore.Qt.AlignCenter)
        self.label_14.setObjectName("label_14")
        self.horizontalLayout_7.addWidget(self.label_14)
        self.stackedWidget_2.addWidget(self.page)
        self.pageHardware = QtWidgets.QWidget()
        self.pageHardware.setObjectName("pageHardware")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.pageHardware)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.labelHardware = QtWidgets.QLabel(self.pageHardware)
        font = QtGui.QFont()
        font.setPointSize(30)
        self.labelHardware.setFont(font)
        self.labelHardware.setObjectName("labelHardware")
        self.verticalLayout_6.addWidget(self.labelHardware)
        self.widgetConnection = QtWidgets.QWidget(self.pageHardware)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.widgetConnection.setFont(font)
        self.widgetConnection.setObjectName("widgetConnection")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.widgetConnection)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.labelHardwareCOM = QtWidgets.QLabel(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelHardwareCOM.setFont(font)
        self.labelHardwareCOM.setAlignment(QtCore.Qt.AlignCenter)
        self.labelHardwareCOM.setObjectName("labelHardwareCOM")
        self.horizontalLayout_4.addWidget(self.labelHardwareCOM)
        self.comboBoxHardwareCOM = QtWidgets.QComboBox(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.comboBoxHardwareCOM.setFont(font)
        self.comboBoxHardwareCOM.setObjectName("comboBoxHardwareCOM")
        self.horizontalLayout_4.addWidget(self.comboBoxHardwareCOM)
        self.labelInstrumentName = QtWidgets.QLabel(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelInstrumentName.setFont(font)
        self.labelInstrumentName.setAlignment(QtCore.Qt.AlignCenter)
        self.labelInstrumentName.setObjectName("labelInstrumentName")
        self.horizontalLayout_4.addWidget(self.labelInstrumentName)
        self.comboBoxInstrumentName = QtWidgets.QComboBox(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.comboBoxInstrumentName.setFont(font)
        self.comboBoxInstrumentName.setObjectName("comboBoxInstrumentName")
        self.comboBoxInstrumentName.addItems(["RCU产品","GPD3303S电源设备"])
        self.horizontalLayout_4.addWidget(self.comboBoxInstrumentName)
        self.labelCommunication = QtWidgets.QLabel(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelCommunication.setFont(font)
        self.labelCommunication.setAlignment(QtCore.Qt.AlignCenter)
        self.labelCommunication.setObjectName("labelCommunication")
        self.horizontalLayout_4.addWidget(self.labelCommunication)
        self.comboBoxCommunication = QtWidgets.QComboBox(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.comboBoxCommunication.setFont(font)
        self.comboBoxCommunication.setObjectName("comboBoxCommunication")
        self.comboBoxCommunication.addItems(["COM"])
        self.horizontalLayout_4.addWidget(self.comboBoxCommunication)
        self.labelProductCOM = QtWidgets.QLabel(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.labelProductCOM.setFont(font)
        self.labelProductCOM.setAlignment(QtCore.Qt.AlignCenter)
        self.labelProductCOM.setObjectName("labelProductCOM")
        self.horizontalLayout_4.addWidget(self.labelProductCOM)
        self.comboBoxProductCOM = QtWidgets.QComboBox(self.widgetConnection)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.comboBoxProductCOM.setFont(font)
        self.comboBoxProductCOM.setObjectName("comboBoxProductCOM")
        self.comboBoxProductCOM.addItems(["AISGIN","AISGOUT","OOK1","OOK2","电源AISGIN"])
        self.horizontalLayout_4.addWidget(self.comboBoxProductCOM)
        self.verticalLayout_6.addWidget(self.widgetConnection)
        self.widgetEdit = QtWidgets.QWidget(self.pageHardware)
        self.widgetEdit.setObjectName("widgetEdit")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.widgetEdit)
        self.horizontalLayout_5.setContentsMargins(20, -1, 20, -1)
        self.horizontalLayout_5.setSpacing(50)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.pushButtonAppend = QtWidgets.QPushButton(self.widgetEdit)
        self.pushButtonAppend.setObjectName("pushButtonAppend")
        self.horizontalLayout_5.addWidget(self.pushButtonAppend)
        self.pushButtonModify = QtWidgets.QPushButton(self.widgetEdit)
        self.pushButtonModify.setObjectName("pushButtonModify")
        self.horizontalLayout_5.addWidget(self.pushButtonModify)
        self.pushButtonDelete = QtWidgets.QPushButton(self.widgetEdit)
        self.pushButtonDelete.setObjectName("pushButtonDelete")
        self.horizontalLayout_5.addWidget(self.pushButtonDelete)
        self.verticalLayout_6.addWidget(self.widgetEdit)
        self.tableHardware = QtWidgets.QTableWidget(self.pageHardware)
        self.tableHardware.verticalHeader().hide()
        font = QtGui.QFont()
        font.setPointSize(10)
        self.tableHardware.setFont(font)
        self.tableHardware.setObjectName("tableHardware")
        self.tableHardware.setColumnCount(5)
        self.tableHardware.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(0, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(1, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(2, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(2, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(3, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(3, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(4, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableHardware.setItem(4, 4, item)
        self.verticalLayout_6.addWidget(self.tableHardware)
        self.widgetHardwareTest = QtWidgets.QWidget(self.pageHardware)
        self.widgetHardwareTest.setObjectName("widgetHardwareTest")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.widgetHardwareTest)
        self.horizontalLayout_6.setContentsMargins(30, -1, 30, -1)
        self.horizontalLayout_6.setSpacing(100)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.pushButtonHardwareTest = QtWidgets.QPushButton(self.widgetHardwareTest)
        self.pushButtonHardwareTest.setObjectName("pushButtonHardwareTest")
        self.horizontalLayout_6.addWidget(self.pushButtonHardwareTest)
        self.pushButtonSave = QtWidgets.QPushButton(self.widgetHardwareTest)
        self.pushButtonSave.setObjectName("pushButtonSave")
        self.horizontalLayout_6.addWidget(self.pushButtonSave)
        self.verticalLayout_6.addWidget(self.widgetHardwareTest)
        self.verticalLayout_6.setStretch(0, 1)
        self.verticalLayout_6.setStretch(1, 1)
        self.verticalLayout_6.setStretch(2, 1)
        self.verticalLayout_6.setStretch(3, 6)
        self.verticalLayout_6.setStretch(4, 1)
        self.stackedWidget_2.addWidget(self.pageHardware)
        self.horizontalLayout.addWidget(self.stackedWidget_2)
        self.horizontalLayout.setStretch(0, 1)
        self.horizontalLayout.setStretch(1, 2)
        self.verticalLayout.addWidget(self.widget)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setEnabled(True)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1123, 23))
        self.menubar.setObjectName("menubar")
        self.System = QtWidgets.QMenu(self.menubar)
        self.System.setObjectName("System")
        self.Hardware = QtWidgets.QMenu(self.menubar)
        self.Hardware.setObjectName("Hardware")
        self.Program = QtWidgets.QMenu(self.menubar)
        self.Program.setObjectName("Program")
        self.Product = QtWidgets.QMenu(self.menubar)
        self.Product.setObjectName("Product")
        self.Permission = QtWidgets.QMenu(self.menubar)
        self.Permission.setObjectName("Permission")
        self.About = QtWidgets.QMenu(self.menubar)
        self.About.setObjectName("About")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.menubar.addAction(self.System.menuAction())
        self.menubar.addAction(self.Hardware.menuAction())
        self.menubar.addAction(self.Program.menuAction())
        self.menubar.addAction(self.Product.menuAction())
        self.menubar.addAction(self.Permission.menuAction())
        self.menubar.addAction(self.About.menuAction())

        self.retranslateUi(MainWindow)
        self.stackedWidget_2.setCurrentIndex(0)
        self.tabWidgetResult.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        self.labelWelcome.setText(_translate("MainWindow", "欢迎使用"))
        self.pushButtonMES.setText(_translate("MainWindow", "MES设置"))
        self.pushButtonSystem.setText(_translate("MainWindow", "系统上传"))
        self.pushButtonProgram.setText(_translate("MainWindow", "程序编辑"))
        self.pushButtonPermission.setText(_translate("MainWindow", "权限管理"))
        self.pushButtonHardware.setText(_translate("MainWindow", "硬件配置"))
        self.pushButtonProduct.setText(_translate("MainWindow", "产品测试"))
        self.pushButtonPassword.setText(_translate("MainWindow", "密码修改"))
        self.pushButtonExit.setText(_translate("MainWindow", "退出系统"))
        self.pushButtonLoadProgram.setText(_translate("MainWindow", "程式加载"))
        self.pushButtonStartTest.setText(_translate("MainWindow", "开始测试"))
        self.pushButtonExitSystem.setText(_translate("MainWindow", "退出系统"))
        self.tabWidgetResult.setTabText(self.tabWidgetResult.indexOf(self.tabTestData), _translate("MainWindow", "测试结果"))
        self.tabWidgetResult.setTabText(self.tabWidgetResult.indexOf(self.tabData), _translate("MainWindow", "底层数据"))
        self.labelShowName.setText(_translate("MainWindow", "测试名称"))
        self.labelShowText.setText(_translate("MainWindow", "测试内容"))
        self.labelShowCOM.setText(_translate("MainWindow", "测试端口"))
        self.labelShowFormat.setText(_translate("MainWindow", "测试规格"))
        self.labelShowFileType.setText(_translate("MainWindow", "文件类型"))
        self.labelShowFile.setText(_translate("MainWindow", "模块文件"))
        self.toolButtonShowFile.setText(_translate("MainWindow", "..."))
        self.labelShowCode.setText(_translate("MainWindow", "产品编码"))
        self.pushButtonMoveItem.setText(_translate("MainWindow", "移动测项"))
        self.pushButtonMoveFront.setText(_translate("MainWindow", "前移测项"))
        self.pushButton_MoveBehind.setText(_translate("MainWindow", "后移测项"))
        self.pushButtonAddItem.setText(_translate("MainWindow", "添加测项"))
        self.pushButtonDeleteItem.setText(_translate("MainWindow", "删除测项"))
        self.pushButtonOpenFile.setText(_translate("MainWindow", "打开文件"))
        self.pushButtonSaveFile.setText(_translate("MainWindow", "保存文件"))
        self.pushButtonExitSys.setText(_translate("MainWindow", "退出系统"))
        __sortingEnabled = self.treeCMD.isSortingEnabled()
        self.treeCMD.setSortingEnabled(False)
        self.treeCMD.topLevelItem(0).setText(0, _translate("MainWindow", "广播进入装备模式"))
        self.treeCMD.topLevelItem(1).setText(0, _translate("MainWindow", "广播恢复出厂序列号"))
        self.treeCMD.topLevelItem(2).setText(0, _translate("MainWindow", "默认序列号分配地址"))
        self.treeCMD.topLevelItem(3).setText(0, _translate("MainWindow", "设备1分配地址并建立连接"))
        self.treeCMD.topLevelItem(4).setText(0, _translate("MainWindow", "设备2分配地址并建立连接"))
        self.treeCMD.topLevelItem(5).setText(0, _translate("MainWindow", "查询软硬件版本信息"))
        self.treeCMD.topLevelItem(6).setText(0, _translate("MainWindow", "设备设置装备模式"))
        self.treeCMD.topLevelItem(7).setText(0, _translate("MainWindow", "设置参数协商"))
        self.treeCMD.topLevelItem(8).setText(0, _translate("MainWindow", "写温度信息"))
        self.treeCMD.topLevelItem(9).setText(0, _translate("MainWindow", "回读温度信息"))
        self.treeCMD.topLevelItem(10).setText(0, _translate("MainWindow", "Flash MINI自检"))
        self.treeCMD.topLevelItem(11).setText(0, _translate("MainWindow", "写电子标签"))
        self.treeCMD.topLevelItem(12).setText(0, _translate("MainWindow", "写合路器信息文件"))
        self.treeCMD.topLevelItem(13).setText(0, _translate("MainWindow", "设置假负载开关"))
        self.treeCMD.topLevelItem(14).setText(0, _translate("MainWindow", "天线校准命令"))
        self.treeCMD.topLevelItem(15).setText(0, _translate("MainWindow", "读取电机转速"))
        self.treeCMD.topLevelItem(16).setText(0, _translate("MainWindow", "设置天线倾角"))
        self.treeCMD.topLevelItem(17).setText(0, _translate("MainWindow", "对设备分配地址并建链"))
        self.treeCMD.topLevelItem(18).setText(0, _translate("MainWindow", "写RCU序列号"))
        self.treeCMD.topLevelItem(19).setText(0, _translate("MainWindow", "备份序列号"))
        self.treeCMD.topLevelItem(20).setText(0, _translate("MainWindow", "回读电子标签"))
        self.treeCMD.topLevelItem(21).setText(0, _translate("MainWindow", "回读合路器信息文件"))
        self.treeCMD.topLevelItem(22).setText(0, _translate("MainWindow", "回读RCU序列号"))
        self.treeCMD.topLevelItem(23).setText(0, _translate("MainWindow", "回读备份序列号"))
        self.treeCMD.topLevelItem(24).setText(0, _translate("MainWindow", "------------电源指令-----------"))
        self.treeCMD.topLevelItem(25).setText(0, _translate("MainWindow", "查询上位机状态"))
        self.treeCMD.topLevelItem(26).setText(0, _translate("MainWindow", "切换供电开关"))
        self.treeCMD.topLevelItem(27).setText(0, _translate("MainWindow", "查询供电开关"))
        self.treeCMD.topLevelItem(28).setText(0, _translate("MainWindow", "读取电压值"))
        self.treeCMD.topLevelItem(29).setText(0, _translate("MainWindow", "读取电流值"))
        self.treeCMD.topLevelItem(30).setText(0, _translate("MainWindow", "------------温度指令-----------"))
        self.treeCMD.topLevelItem(31).setText(0, _translate("MainWindow", "查询温度值"))
        self.treeCMD.topLevelItem(32).setText(0, _translate("MainWindow", "------------系统指令-----------"))
        self.treeCMD.topLevelItem(33).setText(0, _translate("MainWindow", "延时函数"))
        self.treeCMD.setSortingEnabled(__sortingEnabled)
        __sortingEnabled = self.tableWidget_2.isSortingEnabled()
        self.tableWidget_2.setSortingEnabled(False)
        item = self.tableWidget_2.item(0, 1)
        item.setText(_translate("MainWindow", "测试端口"))
        item = self.tableWidget_2.item(0, 2)
        item.setText(_translate("MainWindow", "测试名称"))
        item = self.tableWidget_2.item(0, 3)
        item.setText(_translate("MainWindow", "测试内容"))
        item = self.tableWidget_2.item(0, 4)
        item.setText(_translate("MainWindow", "测试规格"))
        item = self.tableWidget_2.item(0, 5)
        item.setText(_translate("MainWindow", "文件类型"))
        item = self.tableWidget_2.item(0, 6)
        item.setText(_translate("MainWindow", "文件名称"))
        item = self.tableWidget_2.item(1, 0)
        item.setText(_translate("MainWindow", "1"))
        self.tableWidget_2.setSortingEnabled(__sortingEnabled)
        self.label_21.setText(_translate("MainWindow", "权限配置"))
        self.labelHardware.setText(_translate("MainWindow", "硬件配置模块"))
        self.labelHardwareCOM.setText(_translate("MainWindow", "硬件接口"))
        self.labelInstrumentName.setText(_translate("MainWindow", "设备名称"))
        self.labelCommunication.setText(_translate("MainWindow", "通信类型"))
        self.labelProductCOM.setText(_translate("MainWindow", "产品接口"))
        self.pushButtonAppend.setText(_translate("MainWindow", "添加"))
        self.pushButtonModify.setText(_translate("MainWindow", "修改"))
        self.pushButtonDelete.setText(_translate("MainWindow", "删除"))

        item = self.tableHardware.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "1"))
        item = self.tableHardware.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "2"))
        item = self.tableHardware.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "3"))
        item = self.tableHardware.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "4"))
        item = self.tableHardware.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "5"))
        item = self.tableHardware.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "硬件接口"))
        item = self.tableHardware.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "设备名称"))
        item = self.tableHardware.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "通信类型"))
        item = self.tableHardware.horizontalHeaderItem(4)
        item.setText(_translate("MainWindow", "产品接口"))
        __sortingEnabled = self.tableHardware.isSortingEnabled()
        self.tableHardware.setSortingEnabled(False)
        item = self.tableHardware.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.tableHardware.item(0, 1)
        item.setText(_translate("MainWindow", "COM7"))
        item = self.tableHardware.item(0, 2)
        item.setText(_translate("MainWindow", "RCU产品"))
        item = self.tableHardware.item(0, 3)
        item.setText(_translate("MainWindow", "COM"))
        item = self.tableHardware.item(0, 4)
        item.setText(_translate("MainWindow", "AISGIN"))
        item = self.tableHardware.item(1, 0)
        item.setText(_translate("MainWindow", "1"))
        item = self.tableHardware.item(1, 1)
        item.setText(_translate("MainWindow", "COM4"))
        item = self.tableHardware.item(1, 2)
        item.setText(_translate("MainWindow", "RCU产品"))
        item = self.tableHardware.item(1, 3)
        item.setText(_translate("MainWindow", "COM"))
        item = self.tableHardware.item(1, 4)
        item.setText(_translate("MainWindow", "OOK1"))
        item = self.tableHardware.item(2, 0)
        item.setText(_translate("MainWindow", "2"))
        item = self.tableHardware.item(2, 1)
        item.setText(_translate("MainWindow", "COM5"))
        item = self.tableHardware.item(2, 2)
        item.setText(_translate("MainWindow", "RCU产品"))
        item = self.tableHardware.item(2, 3)
        item.setText(_translate("MainWindow", "COM"))
        item = self.tableHardware.item(2, 4)
        item.setText(_translate("MainWindow", "OOK2"))
        item = self.tableHardware.item(3, 0)
        item.setText(_translate("MainWindow", "3"))
        item = self.tableHardware.item(3, 1)
        item.setText(_translate("MainWindow", "COM8"))
        item = self.tableHardware.item(3, 2)
        item.setText(_translate("MainWindow", "GPD3303S电源设备"))
        item = self.tableHardware.item(3, 3)
        item.setText(_translate("MainWindow", "COM"))
        item = self.tableHardware.item(3, 4)
        item.setText(_translate("MainWindow", "电源AISGIN"))
        item = self.tableHardware.item(4, 0)
        item.setText(_translate("MainWindow", "4"))
        item = self.tableHardware.item(4, 1)
        item.setText(_translate("MainWindow", "COM1"))
        item = self.tableHardware.item(4, 2)
        item.setText(_translate("MainWindow", "GPD3303S电源设备"))
        item = self.tableHardware.item(4, 3)
        item.setText(_translate("MainWindow", "COM"))
        item = self.tableHardware.item(4, 4)
        item.setText(_translate("MainWindow", "电源AISGOUT"))
        self.tableHardware.setSortingEnabled(__sortingEnabled)

        self.pushButtonHardwareTest.setText(_translate("MainWindow", "硬件测试"))
        self.pushButtonSave.setText(_translate("MainWindow", "保存"))
        self.System.setTitle(_translate("MainWindow", "系统交互"))
        self.Hardware.setTitle(_translate("MainWindow", "硬件信息"))
        self.Program.setTitle(_translate("MainWindow", "程序编辑"))
        self.Product.setTitle(_translate("MainWindow", "产品测试"))
        self.Permission.setTitle(_translate("MainWindow", "权限管理"))
        self.About.setTitle(_translate("MainWindow", "关于"))
