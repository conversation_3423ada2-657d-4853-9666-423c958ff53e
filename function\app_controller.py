# -*- coding: utf-8 -*-
"""
应用控制器模块
负责协调各个子模块，处理业务逻辑和模块间的通信
"""

from PyQt5 import QtWidgets
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Dict, Optional
import os

# 导入各个子模块
from .config_manager import ConfigManager
from .logger_manager import LoggerManager
from .file_manager import FileManager
from .serial_manager import SerialManager
from .command_handler import CommandHandler
from .test_controller import TestController, TestStatus
from .ui_manager import UIManager
from .exception_handler import ExceptionHandler, setup_global_exception_handler

class AppController(QObject):
    """应用程序主控制器"""
    
    # 信号定义
    status_changed = pyqtSignal(str)
    test_progress = pyqtSignal(int, int)
    test_completed = pyqtSignal(bool, str)
    
    def __init__(self, ui):
        super().__init__()
        self.ui = ui
        
        # 初始化各个管理器
        self._init_managers()
        
        # 初始化UI管理器
        self._init_ui_manager()
        
        # 初始化测试控制器
        self._init_test_controller()
        
        # 设置UI和测试的集成
        self._setup_ui_test_integration()
        
        # 设置异常处理
        self._setup_exception_handling()
        
        # 连接信号槽
        self._connect_signals()
        
        # 加载配置
        self._load_configurations()
        
        self.logger_manager.log_info("应用控制器初始化完成")
    
    def _init_managers(self):
        """初始化各个管理器"""
        try:
            # 日志管理器
            self.logger_manager = LoggerManager()
            
            # 配置管理器
            self.config_manager = ConfigManager()
            
            # 文件管理器
            self.file_manager = FileManager(self.logger_manager)
            
            # 串口管理器
            self.serial_manager = SerialManager(self.logger_manager)
            
            # 指令处理器
            self.command_handler = CommandHandler()
            
            # 异常处理器
            self.exception_handler = ExceptionHandler(self.logger_manager)
            
        except Exception as e:
            print(f"初始化管理器失败: {e}")
            raise
    
    def _init_ui_manager(self):
        """初始化UI管理器"""
        try:
            self.ui_manager = UIManager(self.ui, self.config_manager, self.logger_manager)
            
            # 设置测试开始回调
            self.ui_manager.set_test_start_callback(self._on_test_start_requested)
            
        except Exception as e:
            self.logger_manager.log_error("初始化UI管理器失败", "error", e)
            raise
    
    def _init_test_controller(self):
        """初始化测试控制器"""
        try:
            self.test_controller = TestController(
                self.serial_manager,
                self.command_handler,
                self.config_manager,
                self.logger_manager,
                self.file_manager
            )
            
            # 设置UI组件引用
            self.test_controller.set_ui_components(
                self.ui.tableWidget_2,
                self._on_test_status_changed
            )
            
        except Exception as e:
            self.logger_manager.log_error("初始化测试控制器失败", "error", e)
            raise
    
    def _setup_exception_handling(self):
        """设置异常处理"""
        try:
            # 设置全局异常处理器
            setup_global_exception_handler(self.logger_manager)
            
            # 注册资源清理回调
            self.exception_handler.register_cleanup_callback(self._cleanup_resources)
            
        except Exception as e:
            self.logger_manager.log_error("设置异常处理失败", "error", e)
    
    def _connect_signals(self):
        """连接信号槽"""
        try:
            # 测试控制器信号
            self.test_controller.test_started.connect(self._on_test_started)
            self.test_controller.test_completed.connect(self._on_test_completed)
            self.test_controller.step_started.connect(self._on_test_step_started)
            self.test_controller.step_completed.connect(self._on_test_step_completed)
            self.test_controller.status_changed.connect(self._on_test_status_changed)
            self.test_controller.progress_updated.connect(self._on_test_progress_updated)
            
            # UI更新信号（线程安全）
            self.test_controller.ui_append_back_data.connect(self.ui_manager.append_back_data)
            self.test_controller.ui_append_result_data.connect(self.ui_manager.append_result_data)
            
        except Exception as e:
            self.logger_manager.log_error("连接信号槽失败", "error", e)
    
    def _load_configurations(self):
        """加载配置"""
        try:
            # 只刷新UI中的硬件列表显示
            self.ui_manager.refresh_com_ports()
            
            self.logger_manager.log_info("配置初始化完成")
            
        except Exception as e:
            self.logger_manager.log_error("配置初始化失败", "error", e)
    
    def _on_test_start_requested(self, barcode: str, internal_sn: str, test_data: Dict):
        """处理测试开始请求"""
        try:
            # 首先重置所有状态，确保从头开始
            self._reset_test_state()
            
            # 清空之前的测试结果，并重置labelShow状态
            self.ui_manager.clear_test_results(reset_label=True)
            
            # 加载测试数据到测试控制器
            if not self.test_controller.load_test_data(test_data):
                self.ui_manager.show_message("错误", "加载测试数据失败", "error")
                return
            
            # 开始测试
            if self.test_controller.start_test(barcode, internal_sn):
                # 更新UI状态为运行中，但不显示信息框
                self.ui_manager.update_label_show_color("running")
            else:
                self.ui_manager.show_message("错误", "启动测试失败", "error")
                
        except Exception as e:
            self.logger_manager.log_error("处理测试开始请求失败", "error", e)
            self.ui_manager.show_message("错误", "启动测试失败", "error")
    
    def _on_test_started(self):
        """处理测试开始事件"""
        try:
            self.ui_manager.update_test_status("测试进行中...")
            self.ui_manager.update_label_show_color("running")
            self.status_changed.emit("测试进行中")
            
        except Exception as e:
            self.logger_manager.log_error("处理测试开始事件失败", "error", e)
    
    def _on_test_completed(self, success: bool, message: str):
        """处理测试完成事件"""
        try:
            # 确保串口关闭
            self.serial_manager.close_port()
            
            # 更新UI状态
            if success:
                self.ui_manager.update_label_show_color("completed")
                self.ui_manager.update_test_status("测试完成 - 通过")
                self.ui_manager.show_message("测试完成", "所有测试步骤已成功完成！")
            else:
                self.ui_manager.update_label_show_color("failed")
                self.ui_manager.update_test_status(f"测试失败 - {message}")
                self.ui_manager.show_message("测试失败", f"测试失败：{message}", "error")
            
            # 获取测试摘要
            test_summary = self.test_controller.get_test_summary()
            
            # 添加最终结果到UI
            self.ui_manager.append_final_result(
                test_summary["total_steps"],
                test_summary["passed_steps"], 
                test_summary["failed_steps"]
            )
            
            # 记录测试完成
            self.logger_manager.log_info(f"测试完成 - {test_summary}")
            
            # 发送测试完成信号
            self.test_completed.emit(success, message)
            
            # 不再重置测试状态，保持失败状态显示
            
        except Exception as e:
            self.logger_manager.log_error("处理测试完成事件失败", "error", e)
    
    def _on_test_step_started(self, row: int, command_name: str):
        """处理测试步骤开始事件"""
        try:
            status = f"执行步骤 {row + 1}: {command_name}"
            self.ui_manager.update_test_status(status, row, "yellow")
            
            # 设置pageTest界面的表格行颜色
            self.ui_manager.set_test_table_row_color(row, "yellow")
            
            # 直接更新labelShow颜色为运行状态
            self.ui_manager.update_label_show_color("running")
            self.logger_manager.log_debug(f"步骤 {row + 1} 开始，直接更新labelShow为: running")
            
        except Exception as e:
            self.logger_manager.log_error("处理测试步骤开始事件失败", "error", e)
    
    def _on_test_step_completed(self, row: int, success: bool, message: str):
        """处理测试步骤完成事件"""
        try:
            color = "green" if success else "red"
            result_text = "成功" if success else "失败"
            status = f"步骤 {row + 1} {result_text}"
            if message:
                status += f": {message}"
            
            self.ui_manager.update_test_status(status, row, color)
            
            # 设置pageTest界面的表格行颜色
            self.ui_manager.set_test_table_row_color(row, color)
            
            # 直接更新labelShow颜色
            label_status = "pass" if success else "fail"
            self.ui_manager.update_label_show_color(label_status)
            self.logger_manager.log_debug(f"步骤 {row + 1} 完成，直接更新labelShow为: {label_status}")
            
            # 添加校验结果到textEditResultData
            # 获取命令名称
            if hasattr(self, 'test_controller') and hasattr(self.test_controller, 'test_data'):
                test_data = self.test_controller.test_data
                if row in test_data:
                    command_name = test_data[row].get('名称', f'步骤{row + 1}')
                    self.ui_manager.append_result_data(row, command_name, result_text, message)
            
        except Exception as e:
            self.logger_manager.log_error("处理测试步骤完成事件失败", "error", e)
    
    def _on_test_status_changed(self, status: str):
        """处理测试状态变化事件"""
        try:
            self.ui_manager.update_test_status(f"状态: {status}")
            self.status_changed.emit(status)
            
        except Exception as e:
            self.logger_manager.log_error("处理测试状态变化事件失败", "error", e)
    
    def _on_test_progress_updated(self, current: int, total: int):
        """处理测试进度更新事件"""
        try:
            progress_text = f"进度: {current}/{total}"
            self.ui_manager.update_test_status(progress_text)
            self.test_progress.emit(current, total)
            
        except Exception as e:
            self.logger_manager.log_error("处理测试进度更新事件失败", "error", e)
    
    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 关闭串口
            if hasattr(self, 'serial_manager'):
                self.serial_manager.close_port()
            
            # 停止测试
            if hasattr(self, 'test_controller') and self.test_controller.isRunning():
                self.test_controller.stop_test()
                self.test_controller.wait(3000)  # 等待3秒
            
            self.logger_manager.log_info("资源清理完成")
            
        except Exception as e:
            print(f"资源清理失败: {e}")
    
    # 保持与旧版本的兼容性
    def setup_command_interaction(self):
        """设置命令交互（兼容性方法）"""
        self.logger_manager.log_info("命令交互已通过新架构初始化")
    
    def empty_function(self):
        """空函数（兼容性方法）"""
        pass

    def _setup_ui_test_integration(self):
        """设置UI和测试的集成"""
        try:
            # 设置测试控制器的UI管理器引用
            self.test_controller.set_ui_manager(self.ui_manager)
            
        except Exception as e:
            self.logger_manager.log_error("设置UI和测试集成失败", "error", e)

    def _reset_test_state(self):
        """重置测试状态，确保下次测试能从头开始"""
        try:
            # 停止测试控制器
            if self.test_controller.isRunning():
                self.test_controller.stop_test()
                self.test_controller.wait(3000)  # 等待3秒让线程结束
            
            # 确保串口关闭
            self.serial_manager.close_port()
            
            # 重置测试控制器状态
            self.test_controller._change_status(TestStatus.IDLE)
            self.test_controller.current_row = 0
            self.test_controller.passed_steps = 0
            self.test_controller.failed_steps = 0
            self.test_controller.error_messages = []
            self.test_controller.should_stop = False
            self.test_controller.should_pause = False
            
            # 重置表格颜色
            self.test_controller._reset_table_colors()
            
            # 清空UI显示的测试结果，但不重置labelShow状态
            self.ui_manager.clear_test_results(reset_label=False)
            
            self.logger_manager.log_info("测试状态已重置，可以重新开始测试")
            
        except Exception as e:
            self.logger_manager.log_error("重置测试状态失败", "error", e) 