# -*- coding: utf-8 -*-
"""
文件管理模块
负责处理各种文件操作，包括二进制文件读取、XML文件处理等
"""

import os
import hashlib
from typing import List, Optional, Tuple, Dict
from datetime import datetime

class FileManager:
    """文件管理类"""
    
    def __init__(self, logger_manager=None):
        self.logger_manager = logger_manager
    
    def _log_info(self, message: str):
        """记录信息日志"""
        if self.logger_manager:
            self.logger_manager.log_info(message, "main")
    
    def _log_error(self, message: str, exception: Optional[Exception] = None):
        """记录错误日志"""
        if self.logger_manager:
            self.logger_manager.log_error(message, "error", exception)
    
    def read_binary_file(self, file_path: str) -> Optional[bytes]:
        """
        读取二进制文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bytes: 文件内容，失败返回None
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            with open(file_path, 'rb') as f:
                content = f.read()
            
            self._log_info(f"成功读取二进制文件: {file_path}, 大小: {len(content)} 字节")
            return content
            
        except Exception as e:
            self._log_error(f"读取二进制文件失败: {file_path}", e)
            return None
    
    def write_binary_file(self, file_path: str, data: bytes) -> bool:
        """
        写入二进制文件
        
        Args:
            file_path: 文件路径
            data: 文件数据
            
        Returns:
            bool: 是否写入成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'wb') as f:
                f.write(data)
            
            self._log_info(f"成功写入二进制文件: {file_path}, 大小: {len(data)} 字节")
            return True
            
        except Exception as e:
            self._log_error(f"写入二进制文件失败: {file_path}", e)
            return False
    
    def split_file_to_frames(self, file_path: str, frame_size: int = 0x48) -> List[bytes]:
        """
        将文件分割成帧
        
        Args:
            file_path: 文件路径
            frame_size: 每帧大小
            
        Returns:
            list: 帧数据列表
        """
        try:
            file_data = self.read_binary_file(file_path)
            if file_data is None:
                return []
            
            frames = []
            for i in range(0, len(file_data), frame_size):
                frame = file_data[i:i + frame_size]
                frames.append(frame)
            
            self._log_info(f"文件分帧完成: {file_path}, 总帧数: {len(frames)}")
            return frames
            
        except Exception as e:
            self._log_error(f"文件分帧失败: {file_path}", e)
            return []
    
    def combine_frames_to_data(self, frames: List[bytes]) -> bytes:
        """
        将帧数据合并
        
        Args:
            frames: 帧数据列表
            
        Returns:
            bytes: 合并后的数据
        """
        try:
            combined_data = b''.join(frames)
            self._log_info(f"帧数据合并完成, 总大小: {len(combined_data)} 字节")
            return combined_data
            
        except Exception as e:
            self._log_error("帧数据合并失败", e)
            return b''
    
    def compare_files(self, file1_path: str, file2_path: str) -> bool:
        """
        比较两个文件是否相同
        
        Args:
            file1_path: 文件1路径
            file2_path: 文件2路径
            
        Returns:
            bool: 是否相同
        """
        try:
            data1 = self.read_binary_file(file1_path)
            data2 = self.read_binary_file(file2_path)
            
            if data1 is None or data2 is None:
                return False
            
            result = data1 == data2
            self._log_info(f"文件比较结果: {file1_path} vs {file2_path} = {'相同' if result else '不同'}")
            return result
            
        except Exception as e:
            self._log_error(f"文件比较失败: {file1_path} vs {file2_path}", e)
            return False
    
    def compare_data_with_file(self, data: bytes, file_path: str) -> bool:
        """
        比较数据与文件是否相同
        
        Args:
            data: 数据
            file_path: 文件路径
            
        Returns:
            bool: 是否相同
        """
        try:
            file_data = self.read_binary_file(file_path)
            if file_data is None:
                return False
            
            result = data == file_data
            self._log_info(f"数据与文件比较结果: {file_path} = {'相同' if result else '不同'}")
            return result
            
        except Exception as e:
            self._log_error(f"数据与文件比较失败: {file_path}", e)
            return False
    
    def calculate_file_hash(self, file_path: str, algorithm: str = 'md5') -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5/sha1/sha256)
            
        Returns:
            str: 哈希值，失败返回None
        """
        try:
            data = self.read_binary_file(file_path)
            if data is None:
                return None
            
            if algorithm.lower() == 'md5':
                hash_obj = hashlib.md5()
            elif algorithm.lower() == 'sha1':
                hash_obj = hashlib.sha1()
            elif algorithm.lower() == 'sha256':
                hash_obj = hashlib.sha256()
            else:
                raise ValueError(f"不支持的哈希算法: {algorithm}")
            
            hash_obj.update(data)
            hash_value = hash_obj.hexdigest()
            
            self._log_info(f"文件哈希计算完成: {file_path} ({algorithm}) = {hash_value}")
            return hash_value
            
        except Exception as e:
            self._log_error(f"计算文件哈希失败: {file_path}", e)
            return None
    
    def get_file_info(self, file_path: str) -> Optional[Dict]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 文件信息
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'is_file': os.path.isfile(file_path),
                'is_dir': os.path.isdir(file_path)
            }
            
            return info
            
        except Exception as e:
            self._log_error(f"获取文件信息失败: {file_path}", e)
            return None
    
    def create_backup(self, file_path: str, backup_dir: str = "backups") -> Optional[str]:
        """
        创建文件备份
        
        Args:
            file_path: 原文件路径
            backup_dir: 备份目录
            
        Returns:
            str: 备份文件路径，失败返回None
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"原文件不存在: {file_path}")
            
            # 创建备份目录
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = os.path.basename(file_path)
            name, ext = os.path.splitext(filename)
            backup_filename = f"{name}_{timestamp}{ext}"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # 复制文件
            data = self.read_binary_file(file_path)
            if data is None:
                return None
            
            if not self.write_binary_file(backup_path, data):
                return None
            
            self._log_info(f"文件备份成功: {file_path} -> {backup_path}")
            return backup_path
            
        except Exception as e:
            self._log_error(f"创建文件备份失败: {file_path}", e)
            return None
    
    def generate_electronic_tag_data(self, barcode: str, manufacture_date: str = None) -> bytes:
        """
        生成电子标签数据
        
        Args:
            barcode: 条形码 (20位)
            manufacture_date: 制造日期 (格式: YYYY-MM-DD)
            
        Returns:
            bytes: 电子标签数据
        """
        try:
            if len(barcode) != 20:
                raise ValueError("条形码长度必须为20位")
                
            if manufacture_date is None:
                manufacture_date = datetime.now().strftime('%Y-%m-%d')
            
            # 从20位条形码中提取Item (2-9位，共8位)
            item = barcode[1:9]
            
            # 基础电子标签模板
            base_data = (
                "9A 86 76 01 2F 24 5B 41 72 63 68 69 76 65 73 49 6E 66 6F 20 56 65 72 73 69 6F 6E 5D "
                "0D 0A 2F 24 41 72 63 68 69 76 65 73 49 6E 66 6F 56 65 72 73 69 6F 6E 20 3D 20 33 2E "
                "30 0D 0A 0D 0A 0D 0A 5B 42 6F 61 72 64 20 50 72 6F 70 65 72 74 69 65 73 5D 0D 0A 42 "
                "6F 61 72 64 54 79 70 65 3D 41 49 4D 4D 32 30 44 31 31 76 30 34 0D 0A 42 61 72 43 6F "
                "64 65 3D "
            )
            
            middle_data = (
                " 0D 0A 49 74 65 6D 3D "
            )
            
            description_data = (
                " 0D 0A 44 65 73 63 72 69 70 74 69 6F 6E "
                "3D 41 6E 74 65 6E 6E 61 20 41 63 63 65 73 73 6F 72 79 2C 41 49 4D 4D 32 30 44 31 31 "
                "76 30 34 2C 41 6E 74 65 6E 6E 61 20 49 6E 66 6F 72 6D 61 74 69 6F 6E 20 4D 61 6E 61 "
                "67 65 6D 65 6E 74 20 4D 6F 64 75 6C 65 2C 48 75 61 77 65 69 20 41 6E 74 65 6E 6E 61 "
                "2C 44 43 20 31 30 7D 5E 33 30 56 2C 41 49 53 47 32 2E 30 2C 61 20 70 61 69 72 20 6F "
                "66 20 41 49 53 47 20 63 6F 6E 6E 65 63 74 6F 72 73 2C 31 69 6E 20 31 6F 75 74 0D 0A "
                "4D 61 6E 75 66 61 63 74 75 72 65 64 3D "
            )
            
            end_data = (
                " 0D 0A 56 65 6E 64 6F 72 4E 61 6D 65 3D 48 75 61 77 65 69 0D 0A 49 73 73 75 65 4E 75 "
                "6D 62 65 72 3D 30 30 0D 0A 43 4C 45 49 43 6F 64 65 3D 0D 0A 42 4F 4D 3D 00"
            )
            
            # 将条形码转换为十六进制ASCII
            barcode_hex = ' '.join([f"{ord(c):02X}" for c in barcode])
            
            # 将Item转换为十六进制ASCII
            item_hex = ' '.join([f"{ord(c):02X}" for c in item])
            
            # 将制造日期转换为十六进制ASCII
            date_hex = ' '.join([f"{ord(c):02X}" for c in manufacture_date])
            
            # 组合完整数据
            full_hex_string = base_data + barcode_hex + middle_data + item_hex + description_data + date_hex + end_data
            
            # 转换为字节数据
            hex_bytes = bytes.fromhex(full_hex_string.replace(' ', ''))
            
            self._log_info(f"生成电子标签数据完成, 条形码: {barcode}, Item: {item}, 制造日期: {manufacture_date}")
            return hex_bytes
            
        except Exception as e:
            self._log_error(f"生成电子标签数据失败", e)
            return b''
    
    def validate_file_type(self, file_path: str, expected_types: List[str]) -> bool:
        """
        验证文件类型
        
        Args:
            file_path: 文件路径
            expected_types: 期望的文件扩展名列表
            
        Returns:
            bool: 是否为期望的文件类型
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()
            
            expected_types = [t.lower() if t.startswith('.') else f'.{t.lower()}' 
                            for t in expected_types]
            
            result = ext in expected_types
            self._log_info(f"文件类型验证: {file_path} ({ext}) = {'通过' if result else '失败'}")
            return result
            
        except Exception as e:
            self._log_error(f"文件类型验证失败: {file_path}", e)
            return False

    def split_file_for_read_validation(self, file_path: str, frame_size: int = 0x42) -> List[bytes]:
        """
        为回读验证将文件分割成帧
        
        Args:
            file_path: 文件路径
            frame_size: 每帧数据大小（默认0x42字节，对应0x48信息域长度减去6字节固定头）
            
        Returns:
            list: 帧数据列表
        """
        try:
            file_data = self.read_binary_file(file_path)
            if file_data is None:
                return []
            
            frames = []
            for i in range(0, len(file_data), frame_size):
                frame = file_data[i:i + frame_size]
                frames.append(frame)
            
            self._log_info(f"文件分帧完成（回读验证）: {file_path}, 总帧数: {len(frames)}, 帧大小: {frame_size}")
            return frames
            
        except Exception as e:
            self._log_error(f"文件分帧失败（回读验证）: {file_path}", e)
            return []

    def validate_frame_data_with_file(self, frame_data: bytes, file_path: str, frame_num: int, frame_size: int = 0x42) -> Tuple[bool, str]:
        """
        验证单帧数据与文件对应部分是否一致
        
        Args:
            frame_data: 帧数据
            file_path: 文件路径
            frame_num: 帧编号
            frame_size: 每帧数据大小
            
        Returns:
            tuple: (是否匹配, 详细信息)
        """
        try:
            if not os.path.exists(file_path):
                return False, f"对比文件不存在: {file_path}"
            
            with open(file_path, 'rb') as f:
                # 定位到对应帧的起始位置
                start_pos = frame_num * frame_size
                f.seek(start_pos)
                
                # 读取对应长度的数据
                expected_data = f.read(len(frame_data))
                
                # 比较数据
                is_match = frame_data == expected_data
                
                if is_match:
                    message = f"帧{frame_num}数据验证通过，长度: {len(frame_data)}"
                    self._log_info(message)
                else:
                    message = f"帧{frame_num}数据不匹配，实际长度: {len(frame_data)}, 期望长度: {len(expected_data)}"
                    self._log_error(message)
                    
                    # 如果数据较短，记录详细的十六进制对比
                    if len(frame_data) <= 32 and len(expected_data) <= 32:
                        self._log_info(f"实际数据: {frame_data.hex().upper()}")
                        self._log_info(f"期望数据: {expected_data.hex().upper()}")
                
                return is_match, message
                
        except Exception as e:
            error_msg = f"帧{frame_num}数据验证异常: {str(e)}"
            self._log_error(error_msg, e)
            return False, error_msg

    def collect_read_combiner_frames(self, frames_data: List[bytes]) -> bytes:
        """
        收集回读合路器信息文件的多帧数据并合并
        
        Args:
            frames_data: 各帧数据列表
            
        Returns:
            bytes: 合并后的完整数据
        """
        try:
            if not frames_data:
                return b''
            
            combined_data = b''.join(frames_data)
            self._log_info(f"回读数据合并完成，总帧数: {len(frames_data)}, 总大小: {len(combined_data)} 字节")
            return combined_data
            
        except Exception as e:
            self._log_error("回读数据合并失败", e)
            return b''

    def validate_complete_read_data(self, read_data: bytes, file_path: str) -> Tuple[bool, str]:
        """
        验证完整的回读数据与文件是否一致（方法1：全量比较）
        
        Args:
            read_data: 回读的完整数据
            file_path: 对比文件路径
            
        Returns:
            tuple: (是否匹配, 详细信息)
        """
        try:
            file_data = self.read_binary_file(file_path)
            if file_data is None:
                return False, f"无法读取对比文件: {file_path}"
            
            # 比较数据大小
            if len(read_data) != len(file_data):
                message = f"数据大小不匹配，读取数据: {len(read_data)} 字节, 文件数据: {len(file_data)} 字节"
                self._log_error(message)
                return False, message
            
            # 比较数据内容
            is_match = read_data == file_data
            
            if is_match:
                message = f"完整数据验证通过，大小: {len(read_data)} 字节"
                self._log_info(message)
            else:
                message = f"完整数据内容不匹配，大小: {len(read_data)} 字节"
                self._log_error(message)
                
                # 计算哈希值用于调试
                read_hash = hashlib.md5(read_data).hexdigest()
                file_hash = hashlib.md5(file_data).hexdigest()
                self._log_info(f"读取数据MD5: {read_hash}")
                self._log_info(f"文件数据MD5: {file_hash}")
            
            return is_match, message
            
        except Exception as e:
            error_msg = f"完整数据验证异常: {str(e)}"
            self._log_error(error_msg, e)
            return False, error_msg

    def get_file_frame_count(self, file_path: str, frame_size: int = 0x42) -> int:
        """
        获取文件的总帧数
        
        Args:
            file_path: 文件路径
            frame_size: 每帧数据大小
            
        Returns:
            int: 总帧数
        """
        try:
            if not os.path.exists(file_path):
                return 0
            
            file_size = os.path.getsize(file_path)
            frame_count = (file_size + frame_size - 1) // frame_size  # 向上取整
            
            self._log_info(f"文件帧数计算: {file_path}, 文件大小: {file_size}, 帧大小: {frame_size}, 总帧数: {frame_count}")
            return frame_count
            
        except Exception as e:
            self._log_error(f"计算文件帧数失败: {file_path}", e)
            return 0 