# -*- coding: utf-8 -*-
"""
异常处理模块
负责统一的异常处理、错误分类和资源清理
"""

import sys
import traceback
from enum import Enum
from typing import Optional, Callable, Dict, Any
from contextlib import contextmanager

class ErrorLevel(Enum):
    """错误级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """错误分类枚举"""
    SERIAL_COMMUNICATION = "serial_communication"
    COMMAND_PROCESSING = "command_processing"
    FILE_OPERATION = "file_operation"
    CONFIGURATION = "configuration"
    UI_OPERATION = "ui_operation"
    TEST_EXECUTION = "test_execution"
    HARDWARE_CONNECTION = "hardware_connection"
    DATA_VALIDATION = "data_validation"
    SYSTEM = "system"

class TestException(Exception):
    """测试系统自定义异常基类"""
    
    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.SYSTEM, 
                 level: ErrorLevel = ErrorLevel.ERROR, details: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.level = level
        self.details = details or {}

class SerialCommunicationError(TestException):
    """串口通信错误"""
    
    def __init__(self, message: str, port: str = "", details: Optional[Dict] = None):
        super().__init__(message, ErrorCategory.SERIAL_COMMUNICATION, ErrorLevel.ERROR, details)
        self.port = port

class CommandProcessingError(TestException):
    """指令处理错误"""
    
    def __init__(self, message: str, command_name: str = "", details: Optional[Dict] = None):
        super().__init__(message, ErrorCategory.COMMAND_PROCESSING, ErrorLevel.ERROR, details)
        self.command_name = command_name

class FileOperationError(TestException):
    """文件操作错误"""
    
    def __init__(self, message: str, file_path: str = "", details: Optional[Dict] = None):
        super().__init__(message, ErrorCategory.FILE_OPERATION, ErrorLevel.ERROR, details)
        self.file_path = file_path

class ConfigurationError(TestException):
    """配置错误"""
    
    def __init__(self, message: str, config_key: str = "", details: Optional[Dict] = None):
        super().__init__(message, ErrorCategory.CONFIGURATION, ErrorLevel.ERROR, details)
        self.config_key = config_key

class HardwareConnectionError(TestException):
    """硬件连接错误"""
    
    def __init__(self, message: str, device_name: str = "", details: Optional[Dict] = None):
        super().__init__(message, ErrorCategory.HARDWARE_CONNECTION, ErrorLevel.ERROR, details)
        self.device_name = device_name

class DataValidationError(TestException):
    """数据验证错误"""
    
    def __init__(self, message: str, data_type: str = "", details: Optional[Dict] = None):
        super().__init__(message, ErrorCategory.DATA_VALIDATION, ErrorLevel.WARNING, details)
        self.data_type = data_type

class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self, logger_manager=None):
        self.logger_manager = logger_manager
        self.error_callbacks = {}
        self.cleanup_callbacks = []
        self.error_count = {category: 0 for category in ErrorCategory}
    
    def register_error_callback(self, category: ErrorCategory, callback: Callable):
        """
        注册错误回调函数
        
        Args:
            category: 错误分类
            callback: 回调函数
        """
        if category not in self.error_callbacks:
            self.error_callbacks[category] = []
        self.error_callbacks[category].append(callback)
    
    def register_cleanup_callback(self, callback: Callable):
        """
        注册清理回调函数
        
        Args:
            callback: 清理回调函数
        """
        self.cleanup_callbacks.append(callback)
    
    def handle_exception(self, exception: Exception, context: str = "") -> bool:
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 上下文信息
            
        Returns:
            bool: 是否处理成功
        """
        try:
            # 确定异常类型和级别
            if isinstance(exception, TestException):
                category = exception.category
                level = exception.level
                message = exception.message
                details = exception.details
            else:
                category = ErrorCategory.SYSTEM
                level = ErrorLevel.ERROR
                message = str(exception)
                details = {"type": type(exception).__name__}
            
            # 记录错误统计
            self.error_count[category] += 1
            
            # 构建完整错误信息
            full_message = f"{context}: {message}" if context else message
            
            # 记录日志
            self._log_exception(exception, level, category, full_message, details)
            
            # 调用错误回调函数
            self._call_error_callbacks(category, exception, context)
            
            # 根据错误级别决定是否需要清理资源
            if level in [ErrorLevel.ERROR, ErrorLevel.CRITICAL]:
                self._perform_cleanup()
            
            return True
            
        except Exception as e:
            # 异常处理器本身出现异常
            print(f"Exception handler failed: {e}")
            traceback.print_exc()
            return False
    
    def _log_exception(self, exception: Exception, level: ErrorLevel, 
                      category: ErrorCategory, message: str, details: Dict):
        """
        记录异常日志
        
        Args:
            exception: 异常对象
            level: 错误级别
            category: 错误分类
            message: 错误消息
            details: 详细信息
        """
        if not self.logger_manager:
            return
        
        # 构建详细信息
        detail_str = f" - 分类: {category.value}"
        if details:
            detail_str += f" - 详情: {details}"
        
        log_message = f"{message}{detail_str}"
        
        # 根据错误级别选择日志方法
        if level == ErrorLevel.INFO:
            self.logger_manager.log_info(log_message)
        elif level == ErrorLevel.WARNING:
            self.logger_manager.log_warning(log_message)
        elif level == ErrorLevel.ERROR:
            self.logger_manager.log_error(log_message, "error", exception)
        elif level == ErrorLevel.CRITICAL:
            self.logger_manager.log_error(f"CRITICAL: {log_message}", "error", exception)
    
    def _call_error_callbacks(self, category: ErrorCategory, exception: Exception, context: str):
        """
        调用错误回调函数
        
        Args:
            category: 错误分类
            exception: 异常对象
            context: 上下文信息
        """
        if category in self.error_callbacks:
            for callback in self.error_callbacks[category]:
                try:
                    callback(exception, context)
                except Exception as e:
                    print(f"Error callback failed: {e}")
    
    def _perform_cleanup(self):
        """执行资源清理"""
        for callback in self.cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                print(f"Cleanup callback failed: {e}")
    
    @contextmanager
    def error_context(self, context: str, handle_exceptions: bool = True):
        """
        错误上下文管理器
        
        Args:
            context: 上下文描述
            handle_exceptions: 是否自动处理异常
        """
        try:
            yield
        except Exception as e:
            if handle_exceptions:
                self.handle_exception(e, context)
            else:
                raise
    
    def validate_and_raise(self, condition: bool, message: str, 
                          exception_class: type = TestException, **kwargs):
        """
        验证条件，不满足时抛出异常
        
        Args:
            condition: 验证条件
            message: 错误消息
            exception_class: 异常类
            **kwargs: 异常类的额外参数
        """
        if not condition:
            raise exception_class(message, **kwargs)
    
    def safe_execute(self, func: Callable, *args, default_return=None, **kwargs):
        """
        安全执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            default_return: 异常时的默认返回值
            **kwargs: 函数关键字参数
            
        Returns:
            函数返回值或默认值
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_exception(e, f"执行函数 {func.__name__}")
            return default_return
    
    def get_error_statistics(self) -> Dict[str, int]:
        """
        获取错误统计信息
        
        Returns:
            dict: 错误统计字典
        """
        return {category.value: count for category, count in self.error_count.items()}
    
    def reset_error_statistics(self):
        """重置错误统计"""
        self.error_count = {category: 0 for category in ErrorCategory}
    
    def create_serial_error(self, message: str, port: str = "") -> SerialCommunicationError:
        """创建串口通信错误"""
        return SerialCommunicationError(message, port)
    
    def create_command_error(self, message: str, command_name: str = "") -> CommandProcessingError:
        """创建指令处理错误"""
        return CommandProcessingError(message, command_name)
    
    def create_file_error(self, message: str, file_path: str = "") -> FileOperationError:
        """创建文件操作错误"""
        return FileOperationError(message, file_path)
    
    def create_config_error(self, message: str, config_key: str = "") -> ConfigurationError:
        """创建配置错误"""
        return ConfigurationError(message, config_key)
    
    def create_hardware_error(self, message: str, device_name: str = "") -> HardwareConnectionError:
        """创建硬件连接错误"""
        return HardwareConnectionError(message, device_name)
    
    def create_validation_error(self, message: str, data_type: str = "") -> DataValidationError:
        """创建数据验证错误"""
        return DataValidationError(message, data_type)

# 全局异常处理器实例
global_exception_handler = ExceptionHandler()

def setup_global_exception_handler(logger_manager=None):
    """
    设置全局异常处理器
    
    Args:
        logger_manager: 日志管理器
    """
    global global_exception_handler
    global_exception_handler = ExceptionHandler(logger_manager)
    
    # 设置Python全局异常处理
    sys.excepthook = lambda exc_type, exc_value, exc_traceback: global_exception_handler.handle_exception(
        exc_value, "未捕获的异常"
    )

def get_global_exception_handler() -> ExceptionHandler:
    """获取全局异常处理器"""
    return global_exception_handler 