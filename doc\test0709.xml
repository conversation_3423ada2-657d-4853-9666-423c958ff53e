<?xml version="1.0" ?>
<TestConfig>
  <TestItem com="电源AISGIN" name="查询上位机状态" content="" spec="" file_type="" file_path=""/>
  <TestItem com="电源AISGIN" name="切换供电开关" content="1" spec="" file_type="" file_path=""/>
  <TestItem com="电源AISGIN" name="查询供电开关" content="1" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="广播进入装备模式" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="广播恢复出厂序列号" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="默认序列号分配地址" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="写合路器信息文件" content="" spec="" file_type="模块信息文件" file_path="E:/25work/测试文件/配置文件/模块信息文件(大科）/27151197/LX_DK_MODULE_AIMM20D11v03(1197)_20220527.bin"/>
  <TestItem com="AISGIN" name="写合路器信息文件" content="" spec="" file_type="天线信息文件" file_path="E:/25work/测试文件/配置文件/天线信息文件-27012282-008/HA-ADU4516R13v01-ALL-BANDS-H(01).bin"/>
  <TestItem com="AISGIN" name="广播进入装备模式" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="设备1分配地址并建立连接" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="天线校准命令" content="" spec="" file_type="" file_path=""/>
  <TestItem com="电源AISGIN" name="读取电压值" content="1" spec="" file_type="" file_path=""/>
  <TestItem com="电源AISGIN" name="读取电流值" content="1" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="读取电机转速" content="1" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="设置天线倾角" content="15" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="设备2分配地址并建立连接" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="天线校准命令" content="" spec="" file_type="" file_path=""/>
  <TestItem com="电源AISGIN" name="读取电压值" content="1" spec="" file_type="" file_path=""/>
  <TestItem com="电源AISGIN" name="读取电流值" content="1" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="读取电机转速" content="2" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="设置天线倾角" content="20" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="设备1分配地址并建立连接" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="写RCU序列号" content="" spec="" file_type="" file_path=""/>
  <TestItem com="AISGIN" name="备份序列号" content="" spec="" file_type="" file_path=""/>
</TestConfig>