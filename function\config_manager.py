# -*- coding: utf-8 -*-
"""
配置管理模块
负责管理系统配置、硬件配置和测试参数
"""

import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
from typing import Dict, List, Optional, Any
import os
import logging

class ConfigManager:
    """配置管理类"""
    
    def __init__(self):
        self.hardware_config = {}  # 硬件配置
        self.test_config = {}      # 测试配置
        self.system_config = {}    # 系统配置
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self._load_default_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        self.system_config = {
            "encoding": "utf-8",
            "timeout": 2.0,
            "baud_rate": 9600,
            "max_retries": 10,
            "response_interval": 0.05,
            "min_cmd_interval": 0.1,
            "max_cmd_interval": 0.2
        }
        
        self.test_config = {
            "barcode_length": 20,
            "internal_sn_length": 19,
            "frame_size": 0x48,
            "table_columns": [
                "序号", "COM口", "名称", "内容", "规格", "文件类型", "文件路径"
            ]
        }
    
    def load_hardware_config(self, file_path: str) -> bool:
        """
        加载硬件配置文件
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not os.path.exists(file_path):
                self.logger.warning(f"硬件配置文件不存在: {file_path}")
                return False
                
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            self.hardware_config = {}
            for item in root.findall('.//HardwareItem'):
                name = item.get('name', '')
                port = item.get('port', '')
                device_name = item.get('device_name', '')
                communication_type = item.get('communication_type', 'COM')
                
                if name and port:
                    self.hardware_config[name] = {
                        "port": port,
                        "device_name": device_name,
                        "communication_type": communication_type
                    }
                    
            self.logger.info(f"成功加载硬件配置: {len(self.hardware_config)} 个设备")
            return True
            
        except Exception as e:
            self.logger.error(f"加载硬件配置失败: {e}")
            return False
    
    def save_hardware_config(self, file_path: str) -> bool:
        """
        保存硬件配置文件
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            root = ET.Element("HardwareConfig")
            
            for name, config in self.hardware_config.items():
                item = ET.SubElement(root, "HardwareItem")
                item.set('name', name)
                
                if isinstance(config, dict):
                    # 新格式：包含详细信息
                    item.set('port', config.get('port', ''))
                    item.set('device_name', config.get('device_name', ''))
                    item.set('communication_type', config.get('communication_type', 'COM'))
                else:
                    # 旧格式：仅包含端口
                    item.set('port', config)
                    item.set('device_name', '')
                    item.set('communication_type', 'COM')
            
            # 格式化XML
            rough_string = ET.tostring(root, encoding='unicode')
            reparsed = minidom.parseString(rough_string)
            pretty_xml = reparsed.toprettyxml(indent="  ", encoding=None)
            
            # 移除空行
            lines = [line for line in pretty_xml.split('\n') if line.strip()]
            formatted_xml = '\n'.join(lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_xml)
                
            self.logger.info(f"硬件配置已保存到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存硬件配置失败: {e}")
            return False
    
    def get_port_by_name(self, device_name: str) -> Optional[str]:
        """
        根据设备名称获取串口
        
        Args:
            device_name: 设备名称
            
        Returns:
            str: 串口名称，未找到返回None
        """
        config = self.hardware_config.get(device_name)
        if isinstance(config, dict):
            return config.get("port")
        elif isinstance(config, str):
            return config
        return None
    
    def add_hardware_device(self, name: str, port: str, device_name: str = "", communication_type: str = "COM"):
        """
        添加硬件设备
        
        Args:
            name: 产品接口名称
            port: 串口名称
            device_name: 设备名称
            communication_type: 通信类型
        """
        self.hardware_config[name] = {
            "port": port,
            "device_name": device_name,
            "communication_type": communication_type
        }
        self.logger.info(f"添加硬件设备: {name} -> {port} ({device_name}, {communication_type})")
    
    def remove_hardware_device(self, name: str) -> bool:
        """
        移除硬件设备
        
        Args:
            name: 产品接口名称
            
        Returns:
            bool: 是否移除成功
        """
        if name in self.hardware_config:
            del self.hardware_config[name]
            self.logger.info(f"移除硬件设备: {name}")
            return True
        return False
    
    def update_hardware_device(self, old_name: str, new_name: str, new_port: str, device_name: str = "", communication_type: str = "COM") -> bool:
        """
        更新硬件设备
        
        Args:
            old_name: 原产品接口名称
            new_name: 新产品接口名称
            new_port: 新串口名称
            device_name: 设备名称
            communication_type: 通信类型
            
        Returns:
            bool: 是否更新成功
        """
        if old_name in self.hardware_config:
            del self.hardware_config[old_name]
            self.hardware_config[new_name] = {
                "port": new_port,
                "device_name": device_name,
                "communication_type": communication_type
            }
            self.logger.info(f"更新硬件设备: {old_name} -> {new_name}({new_port}, {device_name}, {communication_type})")
            return True
        return False
    
    def get_hardware_list(self) -> List[Dict[str, str]]:
        """
        获取硬件设备列表
        
        Returns:
            list: 硬件设备列表
        """
        hardware_list = []
        for name, config in self.hardware_config.items():
            if isinstance(config, dict):
                # 新格式：包含详细信息
                hardware_list.append({
                    "name": name,
                    "port": config.get("port", ""),
                    "device_name": config.get("device_name", ""),
                    "communication_type": config.get("communication_type", "COM")
                })
            else:
                # 旧格式：仅包含端口
                hardware_list.append({
                    "name": name,
                    "port": config,
                    "device_name": "",
                    "communication_type": "COM"
                })
        return hardware_list
    
    def get_config_value(self, category: str, key: str, default=None) -> Any:
        """
        获取配置值
        
        Args:
            category: 配置类别 (system/test/hardware)
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        config_dict = getattr(self, f"{category}_config", {})
        return config_dict.get(key, default)
    
    def set_config_value(self, category: str, key: str, value: Any):
        """
        设置配置值
        
        Args:
            category: 配置类别 (system/test/hardware)
            key: 配置键
            value: 配置值
        """
        config_dict = getattr(self, f"{category}_config", {})
        config_dict[key] = value
        self.logger.info(f"设置配置: {category}.{key} = {value}")
    
    def load_test_file_config(self, file_path: str) -> Dict:
        """
        加载测试文件配置
        
        Args:
            file_path: 测试文件路径
            
        Returns:
            dict: 测试配置数据
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"测试文件不存在: {file_path}")
                
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            test_data = {}
            for i, item in enumerate(root.findall('.//TestItem')):
                row_data = {
                    "序号": str(i + 1),
                    "COM口": item.get('com', ''),
                    "名称": item.get('name', ''),
                    "内容": item.get('content', ''),
                    "规格": item.get('spec', ''),
                    "文件类型": item.get('file_type', ''),
                    "文件路径": item.get('file_path', '')
                }
                test_data[i] = row_data
                
            self.logger.info(f"成功加载测试文件: {len(test_data)} 个测试项")
            return test_data
            
        except Exception as e:
            self.logger.error(f"加载测试文件失败: {e}")
            raise
    
    def save_test_file_config(self, file_path: str, test_data: Dict) -> bool:
        """
        保存测试文件配置
        
        Args:
            file_path: 测试文件路径
            test_data: 测试数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            root = ET.Element("TestConfig")
            
            for _, row_data in test_data.items():
                item = ET.SubElement(root, "TestItem")
                item.set('com', row_data.get('COM口', ''))
                item.set('name', row_data.get('名称', ''))
                item.set('content', row_data.get('内容', ''))
                item.set('spec', row_data.get('规格', ''))
                item.set('file_type', row_data.get('文件类型', ''))
                item.set('file_path', row_data.get('文件路径', ''))
            
            # 格式化XML
            rough_string = ET.tostring(root, encoding='unicode')
            reparsed = minidom.parseString(rough_string)
            pretty_xml = reparsed.toprettyxml(indent="  ", encoding=None)
            
            # 移除空行
            lines = [line for line in pretty_xml.split('\n') if line.strip()]
            formatted_xml = '\n'.join(lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_xml)
                
            self.logger.info(f"测试配置已保存到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存测试配置失败: {e}")
            return False
    
    def validate_barcode(self, barcode: str) -> bool:
        """
        验证条形码格式
        
        Args:
            barcode: 条形码
            
        Returns:
            bool: 是否有效
        """
        return (len(barcode) == self.test_config["barcode_length"] 
                and barcode.isalnum())
    
    def validate_internal_sn(self, internal_sn: str) -> bool:
        """
        验证内部序列号格式
        
        Args:
            internal_sn: 内部序列号
            
        Returns:
            bool: 是否有效
        """
        return (len(internal_sn) == self.test_config["internal_sn_length"] 
                and internal_sn.isalnum()) 