# 系统升级日志

## 版本 2.1.0 (2025-07-08)

### 重大更新：新增电源指令集

根据改进需求文档，本次升级新增了完整的电源控制指令集，实现了ASCII格式的电源通信协议。

#### 新增功能

##### 1. 电源指令集核心功能
- **查询上位机状态指令集**
  - 包含指令序列：REMOTE, OUT0, VSET1:12, VSET2:12, ISET1:2, ISET2:2, *IDN?
  - 当收到*IDN?的设备信息回传时，表示指令集发送成功
  
- **供电开关控制指令**
  - OUT1：切换供电开关指令，无需回传数据验证
  - STATUS：查询供电开关指令，返回8位二进制数据
  
- **电压电流读取指令**
  - ISET<X>?：读取电流值，<X>为用户输入的端口号
  - VSET<X>?：读取电压值，<X>为用户输入的端口号
  - 支持ASCII格式响应解析（x.xxxA 和 xx.xxxV格式）

##### 2. 校准期间特殊读取逻辑
- **基本校准读取**
  - 校准时间：30秒
  - 读取次数：100次
  - 结果处理：取最大值显示
  
- **交替读取模式**
  - 当电压电流指令同时出现时，交替执行各100次
  - 分别计算各自的最大值
  
- **非阻塞实现**
  - 移除了传统的sleep延时机制
  - 采用短超时(0.1秒)的非阻塞方式
  - 避免影响校准过程的精度

##### 3. 数据格式支持
- **ASCII指令格式**
  - 指令以ASCII字符串形式发送
  - 结尾添加换行符(0x0D 0x0A)
  - 无需CRC校验
  
- **响应解析**
  - 8位二进制状态数据解析
  - 电压电流数值提取
  - 设备信息识别

#### 技术实现

##### 1. 新增类和模块
- **PowerCommandHandler类**
  - 专门处理电源指令的生成、发送和响应解析
  - 实现校准期间的特殊读取逻辑
  - 支持交替读取模式管理
  
- **串口管理器增强**
  - 支持ASCII帧的提取和识别
  - 新增电源指令专用的发送和接收方法
  - 实现混合数据格式的处理

##### 2. 集成方式
- **CommandHandler集成**
  - 将PowerCommandHandler集成到主指令处理器
  - 更新指令列表和参数映射
  - 实现统一的指令生成和验证接口
  
- **SerialManager增强**
  - 支持二进制帧和ASCII帧的混合处理
  - 新增帧类型自动识别功能
  - 实现电源指令序列发送

##### 3. 校准逻辑优化
- **非阻塞读取**
  - 实现`_execute_single_type_reading_non_blocking`方法
  - 实现`_execute_alternating_reading_non_blocking`方法
  - 使用队列管理读取任务，避免阻塞校准过程

#### 测试验证

##### 1. 单元测试
- **TestPowerCommandHandler**：电源指令处理器测试
- **TestSerialManagerPowerSupport**：串口管理器电源支持测试
- **TestCommandHandlerIntegration**：指令处理器集成测试

##### 2. 集成测试
- **基本电源指令流程测试**
- **校准期间特殊读取逻辑测试**
- **错误处理和边界条件测试**

##### 3. 测试结果
- 所有单元测试通过 (10/10)
- 集成测试完全通过
- 校准逻辑验证成功

#### 文件变更

##### 新增文件
- `doc/电源指令集说明.md` - 详细的电源指令集文档
- `test_power_commands.py` - 电源指令单元测试
- `test_power_integration.py` - 电源指令集成测试
- `doc/升级日志.md` - 本升级日志

##### 修改文件
- `function/command_handler.py`
  - 新增PowerCommandHandler类
  - 集成电源指令到主处理器
  - 更新指令列表和参数映射
  
- `function/serial_manager.py`
  - 支持ASCII帧提取和识别
  - 新增电源指令专用方法
  - 实现混合数据格式处理
  
- `技术文档.md`
  - 更新系统功能描述
  - 添加电源指令集说明链接

#### 兼容性

##### 向后兼容
- 现有的二进制指令系统完全保持不变
- 所有原有功能正常工作
- 不影响现有的测试流程

##### 新功能使用
- 电源指令可通过现有的CommandHandler接口使用
- 支持在现有测试流程中集成电源控制
- 提供独立的校准读取API

#### 使用说明

##### 基本使用
```python
# 生成电源指令
cmd = command_handler.generate_command("读取电压值", port="1")

# 发送电源指令
serial_manager.send_power_command(cmd)

# 接收和解析响应
response = serial_manager.receive_power_response()
result = command_handler.power_handler.parse_power_response(response, "读取电压值")
```

##### 校准模式使用
```python
# 开始校准
command_handler.power_handler.start_calibration()

# 执行校准读取
result = command_handler.power_handler.execute_calibration_reading(
    "读取电压值", "1", serial_manager
)

# 停止校准
command_handler.power_handler.stop_calibration()
```

#### 注意事项

1. **数据格式差异**：电源指令使用ASCII格式，与现有二进制指令不同
2. **校准影响**：校准期间的读取采用非阻塞方式，确保不影响校准精度
3. **参数要求**：电压电流读取指令需要指定端口参数
4. **响应验证**：不同电源指令有不同的响应验证逻辑

#### 后续计划

1. **UI界面集成**：将电源指令集成到图形界面
2. **配置文件支持**：添加电源指令的配置选项
3. **性能优化**：进一步优化校准期间的读取性能
4. **扩展功能**：根据实际使用情况添加更多电源控制功能

---

## 历史版本

### 版本 2.0.0 (之前)
- 基础的RCU测试系统
- 二进制指令支持
- 串口通信管理
- 测试流程控制
- 日志记录功能
