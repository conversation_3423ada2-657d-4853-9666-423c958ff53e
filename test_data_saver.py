#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据保存功能测试脚本
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from function.data_saver import DataSaver
from function.logger_manager import <PERSON>ggerManager


def test_data_saver():
    """测试数据保存功能"""
    print("开始测试数据保存功能...")
    
    # 初始化日志管理器和数据保存器
    logger_manager = LoggerManager()
    data_saver = DataSaver(logger_manager)
    
    print(f"数据保存目录: {data_saver.base_save_dir}")
    print(f"支持的指令: {data_saver.supported_commands}")
    
    # 测试1: RCU序列号数据保存
    print("\n=== 测试1: RCU序列号数据保存 ===")
    rcu_data = {
        "valid": True,
        "serial_number": "HW12345678901234567"
    }
    saved_path = data_saver.save_command_response_data("回读RCU序列号", rcu_data)
    if saved_path:
        print(f"✓ RCU序列号数据保存成功: {saved_path}")
        # 验证文件内容
        with open(saved_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"文件内容:\n{content}")
    else:
        print("✗ RCU序列号数据保存失败")
    
    # 测试2: 备份序列号数据保存
    print("\n=== 测试2: 备份序列号数据保存 ===")
    backup_data = {
        "valid": True,
        "backup_serial_number": "BK98765432109876543"
    }
    saved_path = data_saver.save_command_response_data("回读备份序列号", backup_data)
    if saved_path:
        print(f"✓ 备份序列号数据保存成功: {saved_path}")
    else:
        print("✗ 备份序列号数据保存失败")
    
    # 测试3: 软硬件版本信息数据保存
    print("\n=== 测试3: 软硬件版本信息数据保存 ===")
    version_data = {
        "valid": True,
        "version_info": "V1.0.0_20231201",
        "ascii_data": "V1.0.0_20231201",
        "raw_data": "56312E302E305F3230323331323031"
    }
    saved_path = data_saver.save_command_response_data("查询软硬件版本信息", version_data)
    if saved_path:
        print(f"✓ 软硬件版本信息数据保存成功: {saved_path}")
    else:
        print("✗ 软硬件版本信息数据保存失败")
    
    # 测试4: 电机转速数据保存
    print("\n=== 测试4: 电机转速数据保存 ===")
    motor_data = {
        "valid": True,
        "motor_speed": 128
    }
    saved_path = data_saver.save_command_response_data("读取电机转速", motor_data)
    if saved_path:
        print(f"✓ 电机转速数据保存成功: {saved_path}")
    else:
        print("✗ 电机转速数据保存失败")
    
    # 测试5: 电压值数据保存
    print("\n=== 测试5: 电压值数据保存 ===")
    voltage_data = {
        "success": True,
        "raw_response": "12.50",
        "parsed_data": {
            "voltage": 12.50
        }
    }
    saved_path = data_saver.save_command_response_data("读取电压值", voltage_data)
    if saved_path:
        print(f"✓ 电压值数据保存成功: {saved_path}")
    else:
        print("✗ 电压值数据保存失败")
    
    # 测试6: 电流值数据保存
    print("\n=== 测试6: 电流值数据保存 ===")
    current_data = {
        "success": True,
        "raw_response": "2.35",
        "parsed_data": {
            "current": 2.35
        }
    }
    saved_path = data_saver.save_command_response_data("读取电流值", current_data)
    if saved_path:
        print(f"✓ 电流值数据保存成功: {saved_path}")
    else:
        print("✗ 电流值数据保存失败")
    
    # 测试7: 合路器信息文件数据保存（多帧）
    print("\n=== 测试7: 合路器信息文件数据保存 ===")

    # 清理之前的缓存
    data_saver.clear_combiner_cache()

    # 模拟第一帧（大数据，不是最后一帧）
    frame0_data = {
        "valid": True,
        "file_type": "模块信息文件",
        "frame_num": 0,
        "file_data": bytes.fromhex("48656C6C6F20576F726C64" * 6),  # "Hello World" * 6，足够大
        "data_size": 66
    }
    saved_path = data_saver.save_command_response_data("回读合路器信息文件", frame0_data, session_id="test2")
    print(f"第0帧处理结果: {saved_path}")

    # 模拟第二帧（大数据，不是最后一帧）
    frame1_data = {
        "valid": True,
        "file_type": "模块信息文件",
        "frame_num": 1,
        "file_data": bytes.fromhex("54657374204461746121" * 4),  # "Test Data!" * 4，足够大
        "data_size": 40
    }
    saved_path = data_saver.save_command_response_data("回读合路器信息文件", frame1_data, session_id="test2")
    print(f"第1帧处理结果: {saved_path}")

    # 模拟最后一帧（小数据，明确标记为最后一帧）
    frame2_data = {
        "valid": True,
        "file_type": "模块信息文件",
        "frame_num": 2,
        "file_data": bytes.fromhex("454E44"),  # "END"
        "data_size": 3
    }
    saved_path = data_saver.save_command_response_data("回读合路器信息文件", frame2_data,
                                                      session_id="test2", is_last_frame=True)
    if saved_path and saved_path.endswith('.txt'):
        print(f"✓ 合路器信息文件数据保存成功: {saved_path}")
        # 验证二进制文件
        bin_path = saved_path.replace("txt_files", "binary_files").replace(".txt", ".bin")
        if os.path.exists(bin_path):
            with open(bin_path, 'rb') as f:
                bin_content = f.read()
                print(f"二进制文件大小: {len(bin_content)} 字节")
                print(f"二进制文件内容(十六进制): {bin_content.hex().upper()}")

                # 验证数据完整性
                expected_size = 66 + 40 + 3  # 三帧数据的总大小
                if len(bin_content) == expected_size:
                    print("✓ 数据完整性验证通过")
                else:
                    print(f"✗ 数据完整性验证失败，期望{expected_size}字节，实际{len(bin_content)}字节")
        else:
            print(f"✗ 二进制文件不存在: {bin_path}")
    else:
        print("✗ 合路器信息文件数据保存失败")
    
    # 测试8: 不支持的指令
    print("\n=== 测试8: 不支持的指令 ===")
    unsupported_data = {"valid": True, "test": "data"}
    saved_path = data_saver.save_command_response_data("不支持的指令", unsupported_data)
    if saved_path is None:
        print("✓ 正确拒绝了不支持的指令")
    else:
        print("✗ 错误地保存了不支持的指令数据")
    
    print("\n=== 测试完成 ===")
    print(f"保存目录: {data_saver.base_save_dir}")
    print("请检查保存的文件内容是否正确。")


if __name__ == "__main__":
    test_data_saver()
