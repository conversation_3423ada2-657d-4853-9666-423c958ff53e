# -*- coding: utf-8 -*-
"""
界面交互模块
负责处理所有界面相关的操作和数据绑定
"""

from PyQt5 import QtWidgets, QtCore, QtGui
from PyQt5.QtWidgets import QMessageBox, QFileDialog, QInputDialog, QTableWidgetItem
from typing import Dict, List, Optional, Tuple, Callable
from serial.tools import list_ports
import os

class UIManager:
    """界面管理类"""
    
    def __init__(self, ui, config_manager, logger_manager):
        self.ui = ui
        self.config_manager = config_manager
        self.logger_manager = logger_manager
        
        # 初始化界面状态
        self._init_ui_state()
        
        # 连接信号槽
        self._connect_signals()
        
        # 数据存储
        self.test_data = {}
        self.hardware_data = {}
        
    def _init_ui_state(self):
        """初始化界面状态"""
        try:
            # 设置初始页面为硬件配置页面
            self.ui.stackedWidget_2.setCurrentIndex(4)
            
            # 初始化表格
            self._init_test_table()
            
            # 初始化硬件配置表
            self._init_hardware_table()
            
            # 初始化下拉框
            self._init_combo_boxes()
            
            # 隐藏命令树表头
            self.ui.treeCMD.header().hide()
            
            self.logger_manager.log_info("界面初始化完成")
            
        except Exception as e:
            self.logger_manager.log_error("界面初始化失败", "error", e)
    
    def _init_test_table(self):
        """初始化测试表格"""
        try:
            table = self.ui.tableWidget_2
            table.setRowCount(0)
            table.setColumnCount(7)
            table.verticalHeader().hide()
            
            columns = self.config_manager.get_config_value("test", "table_columns", [])
            table.setHorizontalHeaderLabels(columns)
            table.horizontalHeader().setDefaultAlignment(QtCore.Qt.AlignLeft)
            
            # 设置列宽
            table.setColumnWidth(0, 60)   # 序号
            table.setColumnWidth(1, 100)  # COM口
            table.setColumnWidth(2, 200)  # 名称
            table.setColumnWidth(3, 150)  # 内容
            table.setColumnWidth(4, 150)  # 规格
            table.setColumnWidth(5, 120)  # 文件类型
            table.setColumnWidth(6, 250)  # 文件路径
            
        except Exception as e:
            self.logger_manager.log_error("初始化测试表格失败", "error", e)
    
    def _init_hardware_table(self):
        """初始化硬件配置表格"""
        try:
            table = self.ui.tableHardware
            table.setRowCount(0)
            table.setColumnCount(5)
            table.verticalHeader().hide()
            
            # 设置表头
            headers = ["序号", "硬件接口", "设备名称", "通信类型", "产品接口"]
            table.setHorizontalHeaderLabels(headers)
            
            # 设置列宽
            table.setColumnWidth(0, 60)   # 序号
            table.setColumnWidth(1, 100)  # 硬件接口
            table.setColumnWidth(2, 150)  # 设备名称
            table.setColumnWidth(3, 100)  # 通信类型
            table.setColumnWidth(4, 120)  # 产品接口
            
            # 加载现有的硬件数据
            self._load_hardware_data_to_table()
            
        except Exception as e:
            self.logger_manager.log_error("初始化硬件表格失败", "error", e)
    
    def _init_combo_boxes(self):
        """初始化下拉框"""
        try:
            # 初始化pageHardware的硬件接口下拉框
            self._refresh_hardware_com_ports()
            
            # 初始化设备名称下拉框（预设值）
            if hasattr(self.ui, 'comboBoxInstrumentName'):
                self.ui.comboBoxInstrumentName.clear()
                self.ui.comboBoxInstrumentName.addItems([
                    "RCU产品", "GPD3303S电源设备", "天线控制器", "其他设备"
                ])
            
            # 初始化通信类型下拉框（预设值）
            if hasattr(self.ui, 'comboBoxCommunication'):
                self.ui.comboBoxCommunication.clear()
                self.ui.comboBoxCommunication.addItems(["COM", "TCP", "UDP"])
            
            # 初始化产品接口下拉框（预设值）
            if hasattr(self.ui, 'comboBoxProductCOM'):
                self.ui.comboBoxProductCOM.clear()
                self.ui.comboBoxProductCOM.addItems([
                    "AISGIN", "AISGOUT", "OOK1", "OOK2", "电源AISGIN", "电源AISGOUT"
                ])
            
            # 初始化pageSet的COM端口下拉框
            self.refresh_com_ports()
            
            # 初始化文件类型下拉框
            if hasattr(self.ui, 'comboBoxShowFileType'):
                self.ui.comboBoxShowFileType.clear()
                self.ui.comboBoxShowFileType.addItems(["", "模块信息文件", "天线信息文件"])
            
        except Exception as e:
            self.logger_manager.log_error("初始化下拉框失败", "error", e)
    
    def _connect_signals(self):
        """连接信号槽"""
        try:
            # 页面跳转按钮 - 添加界面刷新功能
            self.ui.pushButtonHardware.clicked.connect(
                lambda: self.ui.stackedWidget_2.setCurrentIndex(4))
            self.ui.pushButtonProduct.clicked.connect(self._switch_to_product_page)
            self.ui.pushButtonPermission.clicked.connect(
                lambda: self.ui.stackedWidget_2.setCurrentIndex(2))
            self.ui.pushButtonProgram.clicked.connect(self._switch_to_program_page)
            
            # 退出按钮
            self.ui.pushButtonExit.clicked.connect(self.exit_application)
            self.ui.pushButtonExitSystem.clicked.connect(self.exit_application)
            self.ui.pushButtonExitSys.clicked.connect(self.exit_application)
            
            # pageHardware界面 - 硬件配置按钮
            self.ui.pushButtonAppend.clicked.connect(self.add_hardware_item)
            self.ui.pushButtonDelete.clicked.connect(self.delete_hardware_item)
            self.ui.pushButtonModify.clicked.connect(self.modify_hardware_item)
            
            # pageHardware界面 - 硬件测试和保存按钮（暂时空处理）
            if hasattr(self.ui, 'pushButtonHardwareTest'):
                self.ui.pushButtonHardwareTest.clicked.connect(self.hardware_test_placeholder)
            if hasattr(self.ui, 'pushButtonSave'):
                self.ui.pushButtonSave.clicked.connect(self.save_hardware_placeholder)
            
            # pageHardware界面 - 硬件接口下拉框双击刷新
            if hasattr(self.ui, 'comboBoxHardwareCOM'):
                # 当下拉框点击时刷新COM端口列表
                self.ui.comboBoxHardwareCOM.activated.connect(self._refresh_hardware_com_ports)
            
            # pageHardware界面 - 表格选择变化
            if hasattr(self.ui, 'tableHardware'):
                self.ui.tableHardware.itemSelectionChanged.connect(self.on_hardware_table_selection_changed)
            
            # pageSet程序编辑按钮
            self.ui.pushButtonAddItem.clicked.connect(self.add_test_item)
            self.ui.pushButtonMoveItem.clicked.connect(self.move_item_to_position)
            self.ui.pushButtonMoveFront.clicked.connect(self.move_item_front)
            self.ui.pushButton_MoveBehind.clicked.connect(self.move_item_behind)
            self.ui.pushButtonDeleteItem.clicked.connect(self.delete_test_item)
            self.ui.pushButtonOpenFile.clicked.connect(self.open_test_file_in_set)
            self.ui.pushButtonSaveFile.clicked.connect(self.save_test_file_in_set)
            self.ui.toolButtonShowFile.clicked.connect(self.browse_file)
            
            # pageTest测试按钮
            self.ui.pushButtonLoadProgram.clicked.connect(self.load_test_program_in_test)
            self.ui.pushButtonStartTest.clicked.connect(self.start_test)
            
            # pageSet表格选择变化（tableWidget_2）
            self.ui.tableWidget_2.itemSelectionChanged.connect(self.on_set_table_selection_changed)
            self.ui.treeCMD.itemSelectionChanged.connect(self.on_tree_cmd_selected)
            
            # pageTest表格选择变化（widgetShowTable）
            if hasattr(self.ui, 'widgetShowTable'):
                self.ui.widgetShowTable.itemSelectionChanged.connect(self.on_test_table_selection_changed)
            
            # 编辑控件变化（仅在pageSet界面中生效）
            self._connect_editor_signals()
            
        except Exception as e:
            self.logger_manager.log_error("连接信号槽失败", "error", e)
    
    def _connect_editor_signals(self):
        """连接编辑控件信号"""
        try:
            if hasattr(self.ui, 'lineEditShowName'):
                self.ui.lineEditShowName.textChanged.connect(self.update_table_from_editor)
            if hasattr(self.ui, 'lineEditShowText'):
                self.ui.lineEditShowText.textChanged.connect(self.update_table_from_editor)
            if hasattr(self.ui, 'comboBoxShowCOM'):
                self.ui.comboBoxShowCOM.currentTextChanged.connect(self.update_table_from_editor)
            if hasattr(self.ui, 'lineEditShowFormat'):
                self.ui.lineEditShowFormat.textChanged.connect(self.update_table_from_editor)
            if hasattr(self.ui, 'comboBoxShowFileType'):
                self.ui.comboBoxShowFileType.currentTextChanged.connect(self.update_table_from_editor)
            if hasattr(self.ui, 'lineEditShowFile'):
                self.ui.lineEditShowFile.textChanged.connect(self.update_table_from_editor)
        except Exception as e:
            self.logger_manager.log_error("连接编辑控件信号失败", "error", e)
    
    def exit_application(self):
        """退出应用程序"""
        reply = QMessageBox.question(
            None, '退出确认',
            '确定要退出系统吗？',
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.logger_manager.log_info("用户退出系统")
            QtWidgets.QApplication.quit()
    
    def show_barcode_dialog(self) -> Tuple[bool, str, str]:
        """
        显示条形码输入对话框
        
        Returns:
            tuple: (是否确认, 条形码, 内部序列号)
        """
        try:
            dialog = QtWidgets.QDialog()
            dialog.setWindowTitle("输入设备信息")
            dialog.resize(400, 200)
            
            layout = QtWidgets.QVBoxLayout()
            
            # 条形码输入
            barcode_layout = QtWidgets.QHBoxLayout()
            barcode_layout.addWidget(QtWidgets.QLabel("条形码(20位):"))
            barcode_edit = QtWidgets.QLineEdit()
            barcode_edit.setMaxLength(20)
            barcode_layout.addWidget(barcode_edit)
            layout.addLayout(barcode_layout)
            
            # 内部序列号输入
            sn_layout = QtWidgets.QHBoxLayout()
            sn_layout.addWidget(QtWidgets.QLabel("内部序列号(19位):"))
            sn_edit = QtWidgets.QLineEdit()
            sn_edit.setMaxLength(19)
            sn_layout.addWidget(sn_edit)
            layout.addLayout(sn_layout)
            
            # 按钮
            button_layout = QtWidgets.QHBoxLayout()
            ok_button = QtWidgets.QPushButton("确认")
            cancel_button = QtWidgets.QPushButton("取消")
            button_layout.addWidget(ok_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)
            
            dialog.setLayout(layout)
            
            # 连接按钮信号
            ok_button.clicked.connect(dialog.accept)
            cancel_button.clicked.connect(dialog.reject)
            
            # 显示对话框
            if dialog.exec_() == QtWidgets.QDialog.Accepted:
                barcode = barcode_edit.text().strip()
                internal_sn = sn_edit.text().strip()
                
                # 验证输入
                if not self.config_manager.validate_barcode(barcode):
                    QMessageBox.warning(None, "输入错误", "条形码格式无效！")
                    return False, "", ""
                
                if not self.config_manager.validate_internal_sn(internal_sn):
                    QMessageBox.warning(None, "输入错误", "内部序列号格式无效！")
                    return False, "", ""
                
                return True, barcode, internal_sn
            
            return False, "", ""
            
        except Exception as e:
            self.logger_manager.log_error("显示条形码对话框失败", "error", e)
            return False, "", ""
    
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """
        显示消息框
        
        Args:
            title: 标题
            message: 消息内容
            msg_type: 消息类型 (info/warning/error/question)
        """
        try:
            if msg_type == "info":
                QMessageBox.information(None, title, message)
            elif msg_type == "warning":
                QMessageBox.warning(None, title, message)
            elif msg_type == "error":
                QMessageBox.critical(None, title, message)
            elif msg_type == "question":
                return QMessageBox.question(None, title, message, 
                                          QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes
        except Exception as e:
            self.logger_manager.log_error(f"显示消息框失败: {title}", "error", e)
    
    def add_hardware_item(self):
        """添加硬件设备到表格"""
        try:
            # 获取widgetConnection中的值
            hardware_port = self.ui.comboBoxHardwareCOM.currentText()
            device_name = self.ui.comboBoxInstrumentName.currentText()
            communication_type = self.ui.comboBoxCommunication.currentText()
            product_interface = self.ui.comboBoxProductCOM.currentText()
            
            # 验证输入
            if not hardware_port or not device_name or not communication_type or not product_interface:
                self.show_message("提示", "请完善所有字段后再添加", "warning")
                return
            
            # 检查产品接口是否已存在
            if self._is_product_interface_exists(product_interface):
                self.show_message("警告", f"产品接口 '{product_interface}' 已存在", "warning")
                return
            
            table = self.ui.tableHardware
            row_count = table.rowCount()
            table.insertRow(row_count)
            
            # 设置数据
            table.setItem(row_count, 0, QTableWidgetItem(str(row_count + 1)))  # 序号
            table.setItem(row_count, 1, QTableWidgetItem(hardware_port))       # 硬件接口
            table.setItem(row_count, 2, QTableWidgetItem(device_name))         # 设备名称
            table.setItem(row_count, 3, QTableWidgetItem(communication_type))  # 通信类型
            table.setItem(row_count, 4, QTableWidgetItem(product_interface))   # 产品接口
            
            # 保存到配置管理器
            self.config_manager.add_hardware_device(product_interface, hardware_port, device_name, communication_type)
            
            # 刷新pageSet界面的COM端口下拉框
            self.refresh_com_ports()
            
            self.show_message("成功", f"已添加硬件设备: {product_interface} -> {hardware_port}")
            self.logger_manager.log_info(f"添加硬件设备: {product_interface} -> {hardware_port}")
            
        except Exception as e:
            self.logger_manager.log_error("添加硬件设备失败", "error", e)
            self.show_message("错误", "添加设备失败", "error")
    
    def delete_hardware_item(self):
        """删除硬件设备"""
        try:
            table = self.ui.tableHardware
            current_row = table.currentRow()
            
            if current_row < 0:
                self.show_message("提示", "请先选择要删除的行", "warning")
                return
            
            # 获取产品接口名称
            product_interface_item = table.item(current_row, 4)
            if not product_interface_item:
                self.show_message("错误", "无法获取产品接口信息", "error")
                return
            
            product_interface = product_interface_item.text()
            
            if self.show_message("确认", f"确定要删除设备 '{product_interface}' 吗？", "question"):
                # 从表格中删除
                table.removeRow(current_row)
                
                # 从配置管理器中删除
                self.config_manager.remove_hardware_device(product_interface)
                
                # 更新序号
                self._update_hardware_table_row_numbers()
                
                # 刷新pageSet界面的COM端口下拉框
                self.refresh_com_ports()
                
                self.show_message("成功", f"已删除设备: {product_interface}")
                self.logger_manager.log_info(f"删除硬件设备: {product_interface}")
            
        except Exception as e:
            self.logger_manager.log_error("删除硬件设备失败", "error", e)
            self.show_message("错误", "删除设备失败", "error")
    
    def modify_hardware_item(self):
        """修改硬件设备 - 将widgetConnection的内容填入选中的表格行"""
        try:
            table = self.ui.tableHardware
            current_row = table.currentRow()
            
            if current_row < 0:
                self.show_message("提示", "请先选择要修改的行", "warning")
                return
            
            # 获取widgetConnection中的值
            hardware_port = self.ui.comboBoxHardwareCOM.currentText()
            device_name = self.ui.comboBoxInstrumentName.currentText()
            communication_type = self.ui.comboBoxCommunication.currentText()
            product_interface = self.ui.comboBoxProductCOM.currentText()
            
            # 验证输入
            if not hardware_port or not device_name or not communication_type or not product_interface:
                self.show_message("提示", "请完善所有字段后再修改", "warning")
                return
            
            # 获取原产品接口名称
            old_product_interface_item = table.item(current_row, 4)
            old_product_interface = old_product_interface_item.text() if old_product_interface_item else ""
            
            # 如果产品接口改变了，检查新的是否已存在
            if product_interface != old_product_interface and self._is_product_interface_exists(product_interface):
                self.show_message("警告", f"产品接口 '{product_interface}' 已存在", "warning")
                return
            
            # 更新表格数据
            table.setItem(current_row, 1, QTableWidgetItem(hardware_port))       # 硬件接口
            table.setItem(current_row, 2, QTableWidgetItem(device_name))         # 设备名称
            table.setItem(current_row, 3, QTableWidgetItem(communication_type))  # 通信类型
            table.setItem(current_row, 4, QTableWidgetItem(product_interface))   # 产品接口
            
            # 更新配置管理器
            if old_product_interface != product_interface:
                # 删除旧的，添加新的
                self.config_manager.remove_hardware_device(old_product_interface)
                self.config_manager.add_hardware_device(product_interface, hardware_port, device_name, communication_type)
            else:
                # 只更新现有的
                self.config_manager.update_hardware_device(old_product_interface, product_interface, hardware_port, device_name, communication_type)
            
            # 刷新pageSet界面的COM端口下拉框
            self.refresh_com_ports()
            
            self.show_message("成功", f"已修改设备: {product_interface}")
            self.logger_manager.log_info(f"修改硬件设备: {old_product_interface} -> {product_interface}")
            
        except Exception as e:
            self.logger_manager.log_error("修改硬件设备失败", "error", e)
            self.show_message("错误", "修改设备失败", "error")
    
    def hardware_test_placeholder(self):
        """硬件测试占位函数"""
        self.show_message("提示", "硬件测试功能待实现", "info")
        self.logger_manager.log_info("硬件测试功能被调用（占位）")
    
    def save_hardware_placeholder(self):
        """保存硬件配置占位函数"""
        self.show_message("提示", "保存硬件配置功能待实现", "info")
        self.logger_manager.log_info("保存硬件配置功能被调用（占位）")
    
    def on_hardware_table_selection_changed(self):
        """硬件表格选择变化处理"""
        try:
            table = self.ui.tableHardware
            current_row = table.currentRow()
            
            if current_row >= 0:
                # 将选中行的数据加载到widgetConnection
                hardware_port = self._get_table_item_text(table, current_row, 1)
                device_name = self._get_table_item_text(table, current_row, 2)
                communication_type = self._get_table_item_text(table, current_row, 3)
                product_interface = self._get_table_item_text(table, current_row, 4)
                
                # 设置下拉框的值
                if hasattr(self.ui, 'comboBoxHardwareCOM'):
                    index = self.ui.comboBoxHardwareCOM.findText(hardware_port)
                    if index >= 0:
                        self.ui.comboBoxHardwareCOM.setCurrentIndex(index)
                
                if hasattr(self.ui, 'comboBoxInstrumentName'):
                    index = self.ui.comboBoxInstrumentName.findText(device_name)
                    if index >= 0:
                        self.ui.comboBoxInstrumentName.setCurrentIndex(index)
                
                if hasattr(self.ui, 'comboBoxCommunication'):
                    index = self.ui.comboBoxCommunication.findText(communication_type)
                    if index >= 0:
                        self.ui.comboBoxCommunication.setCurrentIndex(index)
                
                if hasattr(self.ui, 'comboBoxProductCOM'):
                    index = self.ui.comboBoxProductCOM.findText(product_interface)
                    if index >= 0:
                        self.ui.comboBoxProductCOM.setCurrentIndex(index)
                
        except Exception as e:
            self.logger_manager.log_error("处理硬件表格选择变化失败", "error", e)
    
    def _load_hardware_data_to_table(self):
        """加载硬件数据到表格"""
        try:
            table = self.ui.tableHardware
            table.setRowCount(0)
            
            # 不再从配置文件加载，表格保持空白状态
            # 用户可以通过界面手动添加硬件设备
            
            self.logger_manager.log_info("硬件表格已初始化为空白状态")
            
        except Exception as e:
            self.logger_manager.log_error("初始化硬件表格失败", "error", e)
    
    def _update_hardware_table_row_numbers(self):
        """更新硬件表格行号"""
        try:
            table = self.ui.tableHardware
            for row in range(table.rowCount()):
                item = table.item(row, 0)
                if item:
                    item.setText(str(row + 1))
                else:
                    table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
                    
        except Exception as e:
            self.logger_manager.log_error("更新硬件表格行号失败", "error", e)
    
    def _is_product_interface_exists(self, product_interface: str) -> bool:
        """检查产品接口是否已存在"""
        try:
            table = self.ui.tableHardware
            for row in range(table.rowCount()):
                item = table.item(row, 4)  # 产品接口列
                if item and item.text() == product_interface:
                    return True
            return False
        except Exception as e:
            self.logger_manager.log_error("检查产品接口是否存在失败", "error", e)
            return False
    
    def _refresh_hardware_com_ports(self):
        """刷新硬件接口COM端口下拉框"""
        try:
            if hasattr(self.ui, 'comboBoxHardwareCOM'):
                current_text = self.ui.comboBoxHardwareCOM.currentText()
                self.ui.comboBoxHardwareCOM.clear()
                
                # 获取系统可用的串口
                available_ports = [port.device for port in list_ports.comports()]
                self.ui.comboBoxHardwareCOM.addItems([""] + available_ports)
                
                # 恢复之前的选择
                index = self.ui.comboBoxHardwareCOM.findText(current_text)
                if index >= 0:
                    self.ui.comboBoxHardwareCOM.setCurrentIndex(index)
                    
                self.logger_manager.log_debug(f"刷新硬件COM端口: {available_ports}")
                    
        except Exception as e:
            self.logger_manager.log_error("刷新硬件COM端口失败", "error", e)
    
    def add_test_item(self):
        """添加测试项"""
        try:
            # 获取当前选中的命令
            current_item = self.ui.treeCMD.currentItem()
            if not current_item:
                self.show_message("提示", "请先在左侧命令树中选择一个命令", "warning")
                return
            
            command_name = current_item.text(0)
            
            table = self.ui.tableWidget_2
            row_count = table.rowCount()
            table.insertRow(row_count)
            
            # 设置序号
            table.setItem(row_count, 0, QTableWidgetItem(str(row_count + 1)))
            
            # 设置选中命令的信息
            table.setItem(row_count, 1, QTableWidgetItem(""))  # COM口，用户可后续选择
            table.setItem(row_count, 2, QTableWidgetItem(command_name))  # 命令名称
            table.setItem(row_count, 3, QTableWidgetItem(""))  # 内容，根据命令类型可能需要填写
            table.setItem(row_count, 4, QTableWidgetItem(""))  # 规格
            table.setItem(row_count, 5, QTableWidgetItem(""))  # 文件类型
            table.setItem(row_count, 6, QTableWidgetItem(""))  # 文件路径
            
            # 根据命令类型设置默认值
            self._set_command_defaults(row_count, command_name)
            
            # 选中新行
            table.selectRow(row_count)
            
            # 加载新行数据到编辑器
            self._disconnect_editor_signals()
            self._load_row_to_editor(row_count)
            self._connect_editor_signals()
            
            self.logger_manager.log_info(f"添加测试项: {command_name}，当前行数: {row_count + 1}")
            
        except Exception as e:
            self.logger_manager.log_error("添加测试项失败", "error", e)
    
    def _set_command_defaults(self, row: int, command_name: str):
        """
        根据命令类型设置默认值
        
        Args:
            row: 表格行号
            command_name: 命令名称
        """
        try:
            table = self.ui.tableWidget_2
            
            # 根据命令名称设置默认值
            if "写入" in command_name or "烧写" in command_name:
                # 写入类命令可能需要文件
                if "电子标签" in command_name:
                    table.setItem(row, 5, QTableWidgetItem(""))
                elif "模块信息" in command_name:
                    table.setItem(row, 5, QTableWidgetItem("模块信息文件"))
                elif "天线信息" in command_name:
                    table.setItem(row, 5, QTableWidgetItem("天线信息文件"))
            
            elif "回读" in command_name or "读取" in command_name:
                # 读取类命令设置期望的规格值
                if "电子标签" in command_name:
                    table.setItem(row, 4, QTableWidgetItem("待验证"))  # 规格栏显示待验证
                elif "模块信息" in command_name:
                    table.setItem(row, 4, QTableWidgetItem("待验证"))
            
            elif "查询" in command_name:
                # 查询类命令
                table.setItem(row, 4, QTableWidgetItem("查询结果"))
            
            # 根据命令名称推测可能使用的COM口
            if "RCU" in command_name.upper():
                table.setItem(row, 1, QTableWidgetItem("RCU控制"))
            elif "AISG" in command_name.upper():
                table.setItem(row, 1, QTableWidgetItem("AISGIN"))
            elif "天线" in command_name:
                table.setItem(row, 1, QTableWidgetItem("天线控制器"))
            
        except Exception as e:
            self.logger_manager.log_error(f"设置命令默认值失败: {command_name}", "error", e)
    
    def delete_test_item(self):
        """删除测试项"""
        try:
            table = self.ui.tableWidget_2
            current_row = table.currentRow()
            
            if current_row >= 0:
                if self.show_message("确认", f"确定要删除第 {current_row + 1} 行吗？", "question"):
                    table.removeRow(current_row)
                    self._update_row_numbers()
                    self.logger_manager.log_info(f"删除测试项，行号: {current_row + 1}")
            else:
                self.show_message("提示", "请先选择要删除的行", "warning")
                
        except Exception as e:
            self.logger_manager.log_error("删除测试项失败", "error", e)
    
    def move_item_to_position(self):
        """移动项目到指定位置"""
        try:
            table = self.ui.tableWidget_2
            current_row = table.currentRow()
            
            if current_row < 0:
                self.show_message("提示", "请先选择要移动的行", "warning")
                return
            
            position, ok = QInputDialog.getInt(None, "移动行", "目标位置:", 
                                             value=current_row + 1, min=1, max=table.rowCount())
            if ok:
                target_row = position - 1
                if target_row != current_row:
                    self._move_table_row(current_row, target_row)
                    self._update_row_numbers()
                    
        except Exception as e:
            self.logger_manager.log_error("移动测试项失败", "error", e)
    
    def move_item_front(self):
        """向前移动项目"""
        try:
            table = self.ui.tableWidget_2
            current_row = table.currentRow()
            
            if current_row > 0:
                self._move_table_row(current_row, current_row - 1)
                self._update_row_numbers()
                table.selectRow(current_row - 1)
                
        except Exception as e:
            self.logger_manager.log_error("向前移动测试项失败", "error", e)
    
    def move_item_behind(self):
        """向后移动项目"""
        try:
            table = self.ui.tableWidget_2
            current_row = table.currentRow()
            
            if current_row >= 0 and current_row < table.rowCount() - 1:
                self._move_table_row(current_row, current_row + 1)
                self._update_row_numbers()
                table.selectRow(current_row + 1)
                
        except Exception as e:
            self.logger_manager.log_error("向后移动测试项失败", "error", e)
    
    def _move_table_row(self, from_row: int, to_row: int):
        """移动表格行"""
        try:
            table = self.ui.tableWidget_2
            
            # 保存源行数据
            row_data = []
            for col in range(table.columnCount()):
                item = table.item(from_row, col)
                row_data.append(item.text() if item else "")
            
            # 删除源行
            table.removeRow(from_row)
            
            # 在目标位置插入行
            table.insertRow(to_row)
            
            # 恢复数据
            for col, text in enumerate(row_data):
                table.setItem(to_row, col, QTableWidgetItem(text))
                
        except Exception as e:
            self.logger_manager.log_error(f"移动表格行失败: {from_row} -> {to_row}", "error", e)
    
    def _update_row_numbers(self):
        """更新行号"""
        try:
            table = self.ui.tableWidget_2
            for row in range(table.rowCount()):
                item = table.item(row, 0)
                if item:
                    item.setText(str(row + 1))
                else:
                    table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
                    
        except Exception as e:
            self.logger_manager.log_error("更新行号失败", "error", e)
    
    def open_test_file_in_set(self):
        """打开测试文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                None, "打开测试文件", "", "XML文件 (*.xml);;所有文件 (*)"
            )
            
            if file_path:
                test_data = self.config_manager.load_test_file_config(file_path)
                self.load_test_data_to_table(test_data)
                self.show_message("成功", f"已加载测试文件: {os.path.basename(file_path)}")
                
        except Exception as e:
            self.logger_manager.log_error("打开测试文件失败", "error", e)
            self.show_message("错误", "打开文件失败", "error")
    
    def save_test_file_in_set(self):
        """保存测试文件"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                None, "保存测试文件", "", "XML文件 (*.xml);;所有文件 (*)"
            )
            
            if file_path:
                test_data = self.collect_test_data_from_table()
                if self.config_manager.save_test_file_config(file_path, test_data):
                    self.show_message("成功", f"已保存测试文件: {os.path.basename(file_path)}")
                else:
                    self.show_message("错误", "保存文件失败", "error")
                    
        except Exception as e:
            self.logger_manager.log_error("保存测试文件失败", "error", e)
            self.show_message("错误", "保存文件失败", "error")
    
    def browse_file(self):
        """浏览文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                None, "选择文件", "", "所有文件 (*)"
            )
            
            if file_path and hasattr(self.ui, 'lineEditShowFile'):
                self.ui.lineEditShowFile.setText(file_path)
                
        except Exception as e:
            self.logger_manager.log_error("浏览文件失败", "error", e)
    
    def load_test_program_in_test(self):
        """在pageTest界面加载测试程序到widgetShowTable"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                None, "加载测试程序", "", "XML文件 (*.xml);;所有文件 (*)"
            )
            
            if file_path:
                test_data = self.config_manager.load_test_file_config(file_path)
                self.load_test_data_to_test_table(test_data)
                self.show_interface_info(test_data)
                self.show_message("成功", f"已加载测试程序: {os.path.basename(file_path)}")
                
        except Exception as e:
            self.logger_manager.log_error("在pageTest界面加载测试程序失败", "error", e)
            self.show_message("错误", "加载测试程序失败", "error")
    
    def load_test_data_to_test_table(self, test_data: Dict):
        """加载测试数据到pageTest的widgetShowTable"""
        try:
            # 确定使用哪个表格组件
            table = getattr(self.ui, 'widgetShowTable', None)
            if table is None:
                # 如果widgetShowTable不存在，可能是命名不同，尝试其他可能的名称
                table = getattr(self.ui, 'tableViewShowTable', None)
            
            if table is None:
                self.logger_manager.log_error("未找到widgetShowTable组件")
                return
            
            # 清空表格
            table.setRowCount(0)
            table.setColumnCount(7)
            
            # 设置表头
            if hasattr(table, 'setHorizontalHeaderLabels'):
                table.setHorizontalHeaderLabels([
                    "序号", "COM口", "名称", "内容", "规格", "文件类型", "文件路径"
                ])
            
            # 隐藏垂直表头
            if hasattr(table, 'verticalHeader'):
                table.verticalHeader().hide()
            
            # 设置表格为只读
            if hasattr(table, 'setEditTriggers'):
                table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
            
            # 添加数据
            for i, row_data in test_data.items():
                table.insertRow(i)
                table.setItem(i, 0, QTableWidgetItem(row_data.get("序号", str(i + 1))))
                table.setItem(i, 1, QTableWidgetItem(row_data.get("COM口", "")))
                table.setItem(i, 2, QTableWidgetItem(row_data.get("名称", "")))
                table.setItem(i, 3, QTableWidgetItem(row_data.get("内容", "")))
                table.setItem(i, 4, QTableWidgetItem(row_data.get("规格", "")))
                table.setItem(i, 5, QTableWidgetItem(row_data.get("文件类型", "")))
                table.setItem(i, 6, QTableWidgetItem(row_data.get("文件路径", "")))
                
                # 设置初始颜色为白色
                self.set_test_table_row_color(i, "white")
            
            # 设置列宽
            if hasattr(table, 'setColumnWidth'):
                table.setColumnWidth(0, 60)   # 序号
                table.setColumnWidth(1, 100)  # COM口
                table.setColumnWidth(2, 200)  # 名称
                table.setColumnWidth(3, 150)  # 内容
                table.setColumnWidth(4, 150)  # 规格
                table.setColumnWidth(5, 120)  # 文件类型
                table.setColumnWidth(6, 250)  # 文件路径
            
            self.test_data = test_data
            self.logger_manager.log_info(f"测试数据已加载到widgetShowTable，共 {len(test_data)} 行")
            
        except Exception as e:
            self.logger_manager.log_error("加载测试数据到widgetShowTable失败", "error", e)
    
    def show_interface_info(self, test_data: Dict):
        """在textEditShowInfo中显示接口信息"""
        try:
            if not hasattr(self.ui, 'textEditShowInfo'):
                return
            
            info_text = "=== 产品接口与COM口对应关系 ===\n\n"
            
            # 统计使用的COM口
            com_ports = set()
            for row_data in test_data.values():
                com_port = row_data.get("COM口", "")
                if com_port:
                    com_ports.add(com_port)
            
            # 显示COM口映射
            for com_port in sorted(com_ports):
                actual_port = self.config_manager.get_port_by_name(com_port)
                if actual_port:
                    info_text += f"接口: {com_port} → 串口: {actual_port}\n"
                else:
                    info_text += f"接口: {com_port} → 串口: 未配置\n"
            
            info_text += f"\n总计使用 {len(com_ports)} 个接口\n"
            info_text += f"总计 {len(test_data)} 个测试步骤\n\n"
            info_text += "=== 串口状态 ===\n"
            info_text += "等待开始测试...\n"
            
            self.ui.textEditShowInfo.setPlainText(info_text)
            
        except Exception as e:
            self.logger_manager.log_error("显示接口信息失败", "error", e)
    
    def on_test_table_selection_changed(self):
        """pageTest表格选择变化处理"""
        try:
            # pageTest界面的表格是只读的，不需要编辑功能
            # 可以在这里添加显示选中行详细信息的功能
            pass
        except Exception as e:
            self.logger_manager.log_error("处理测试表格选择变化失败", "error", e)
    
    def set_test_table_row_color(self, row: int, color: str):
        """设置pageTest表格行颜色"""
        try:
            table = getattr(self.ui, 'widgetShowTable', None)
            if table is None:
                table = getattr(self.ui, 'tableViewShowTable', None)
            
            if table is None:
                self.logger_manager.log_warning("未找到测试表格组件，无法设置行颜色")
                return
            
            color_map = {
                "yellow": QtGui.QColor(255, 255, 0),
                "green": QtGui.QColor(0, 255, 0),
                "red": QtGui.QColor(255, 0, 0),
                "white": QtGui.QColor(255, 255, 255)
            }
            
            if color in color_map:
                bg_color = color_map[color]
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(bg_color)
            
            # 同时更新labelShow的颜色
            # 将颜色映射到状态
            status_map = {
                "green": "pass",
                "yellow": "running",
                "red": "fail",
                "white": "idle"
            }
            self.update_label_show_color(status_map.get(color, "idle"))
            self.logger_manager.log_debug(f"设置测试表格行 {row} 颜色为 {color}")
                        
        except Exception as e:
            self.logger_manager.log_error(f"设置测试表格行颜色失败: row={row}, color={color}", "error", e)
    
    def update_label_show_color(self, status: str):
        """更新labelShow的颜色"""
        try:
            if not hasattr(self.ui, 'labelShow'):
                self.logger_manager.log_warning("UI中没有找到labelShow组件")
                return
            
            color_map = {
                "idle": "#FFFFFF",        # 白色 - 空闲
                "running": "#FFFF00",     # 黄色 - 测试中
                "pass": "#00FF00",        # 绿色 - 通过
                "completed": "#00FF00",   # 绿色 - 完成
                "fail": "#FF0000",        # 红色 - 失败
                "failed": "#FF0000",      # 红色 - 失败（别名）
                "stopped": "#FFA500"      # 橙色 - 停止
            }
            
            color = color_map.get(status.lower(), "#FFFFFF")
            self.logger_manager.log_debug(f"更新labelShow状态: {status} -> 颜色: {color}")
            
            self.ui.labelShow.setStyleSheet(f"""
                QLabel#labelShow {{
                    border: 1px solid #000;
                    background-color: {color};
                    padding: 5px;
                }}
            """)
            
            # 设置文本
            status_text_map = {
                "idle": "就绪",
                "running": "测试中...",
                "pass": "测试通过",
                "completed": "测试通过",
                "fail": "测试失败",
                "failed": "测试失败",
                "stopped": "测试停止"
            }
            
            text = status_text_map.get(status.lower(), status)
            self.ui.labelShow.setText(text)
            self.logger_manager.log_debug(f"labelShow文本已设置为: {text}")
            
        except Exception as e:
            self.logger_manager.log_error(f"更新labelShow颜色失败: status={status}", "error", e)
    
    def append_back_data(self, row: int, data: str):
        """向textEditBackData添加接收到的数据"""
        try:
            if not hasattr(self.ui, 'textEditBackData'):
                return
            
            current_text = self.ui.textEditBackData.toPlainText()
            new_text = f"[{row + 1:02d}] {data}\n"
            self.ui.textEditBackData.setPlainText(current_text + new_text)
            
            # 滚动到底部
            scrollbar = self.ui.textEditBackData.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            self.logger_manager.log_error(f"添加接收数据失败: row={row}", "error", e)
    
    def append_result_data(self, row: int, command_name: str, result: str, message: str = ""):
        """向textEditResultData添加校验结果"""
        try:
            if not hasattr(self.ui, 'textEditResultData'):
                return
            
            current_text = self.ui.textEditResultData.toPlainText()
            result_text = f"[{row + 1:02d}] {command_name}: {result}"
            if message:
                result_text += f" - {message}"
            result_text += "\n"
            
            self.ui.textEditResultData.setPlainText(current_text + result_text)
            
            # 滚动到底部
            scrollbar = self.ui.textEditResultData.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            self.logger_manager.log_error(f"添加校验结果失败: row={row}", "error", e)
    
    def append_final_result(self, total_steps: int, passed_steps: int, failed_steps: int):
        """添加最终测试结果"""
        try:
            if not hasattr(self.ui, 'textEditResultData'):
                return
            
            current_text = self.ui.textEditResultData.toPlainText()
            final_result = f"\n=== 最终测试结果 ===\n"
            final_result += f"总步骤数: {total_steps}\n"
            final_result += f"通过步骤: {passed_steps}\n"
            final_result += f"失败步骤: {failed_steps}\n"
            final_result += f"测试结果: {'通过' if failed_steps == 0 else '失败'}\n"
            
            self.ui.textEditResultData.setPlainText(current_text + final_result)
            
            # 滚动到底部
            scrollbar = self.ui.textEditResultData.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            self.logger_manager.log_error("添加最终测试结果失败", "error", e)
    
    def update_serial_status(self, port: str, status: str):
        """更新串口状态信息"""
        try:
            if not hasattr(self.ui, 'textEditShowInfo'):
                return
            
            current_text = self.ui.textEditShowInfo.toPlainText()
            
            # 查找并更新串口状态部分
            lines = current_text.split('\n')
            status_section_start = -1
            
            for i, line in enumerate(lines):
                if "=== 串口状态 ===" in line:
                    status_section_start = i
                    break
            
            if status_section_start != -1:
                # 保留前面的接口信息，更新串口状态
                interface_info = '\n'.join(lines[:status_section_start + 1])
                status_info = f"\n当前串口: {port}\n状态: {status}\n"
                self.ui.textEditShowInfo.setPlainText(interface_info + status_info)
            
        except Exception as e:
            self.logger_manager.log_error("更新串口状态失败", "error", e)
    
    def clear_test_results(self, reset_label=True):
        """清空测试结果显示"""
        try:
            if hasattr(self.ui, 'textEditBackData'):
                self.ui.textEditBackData.clear()
            if hasattr(self.ui, 'textEditResultData'):
                self.ui.textEditResultData.clear()
            
            # 只有在需要时才重置labelShow状态
            if reset_label:
                self.update_label_show_color("idle")
            
        except Exception as e:
            self.logger_manager.log_error("清空测试结果失败", "error", e)
    
    def start_test(self):
        """开始测试"""
        try:
            # 获取条形码和序列号
            ok, barcode, internal_sn = self.show_barcode_dialog()
            if not ok:
                return
            
            # 获取测试数据
            test_data = self.collect_test_data_from_table()
            if not test_data:
                self.show_message("警告", "没有测试数据", "warning")
                return
            
            # 触发测试开始事件
            if hasattr(self, 'test_start_callback'):
                self.test_start_callback(barcode, internal_sn, test_data)
            
        except Exception as e:
            self.logger_manager.log_error("开始测试失败", "error", e)
            self.show_message("错误", "开始测试失败", "error")
    
    def on_set_table_selection_changed(self):
        """pageSet界面表格选择变化处理"""
        try:
            table = self.ui.tableWidget_2
            current_row = table.currentRow()
            
            if current_row >= 0:
                self._disconnect_editor_signals()
                self._load_row_to_editor(current_row)
                self._connect_editor_signals()
                
        except Exception as e:
            self.logger_manager.log_error("处理pageSet表格选择变化失败", "error", e)
    
    def on_tree_cmd_selected(self):
        """命令树选择变化处理"""
        try:
            current_item = self.ui.treeCMD.currentItem()
            if current_item:
                command_name = current_item.text(0)
                # 可以在状态栏或其他位置显示选中的命令信息
                self.logger_manager.log_debug(f"选中命令: {command_name}")
                
                # 如果有状态标签，显示当前选中的命令
                if hasattr(self.ui, 'labelSelectedCommand'):
                    self.ui.labelSelectedCommand.setText(f"选中命令: {command_name}")
                
                # 或者在窗口标题中显示
                if hasattr(self.ui, 'groupBoxCMD'):
                    self.ui.groupBoxCMD.setTitle(f"命令列表 - 当前选中: {command_name}")
                
        except Exception as e:
            self.logger_manager.log_error("处理命令树选择变化失败", "error", e)
    
    def update_table_from_editor(self):
        """从编辑器更新表格"""
        try:
            table = self.ui.tableWidget_2
            current_row = table.currentRow()
            
            if current_row >= 0:
                # 更新表格数据
                if hasattr(self.ui, 'comboBoxShowCOM'):
                    self._set_table_item(current_row, 1, self.ui.comboBoxShowCOM.currentText())
                if hasattr(self.ui, 'lineEditShowName'):
                    self._set_table_item(current_row, 2, self.ui.lineEditShowName.text())
                if hasattr(self.ui, 'lineEditShowText'):
                    self._set_table_item(current_row, 3, self.ui.lineEditShowText.text())
                if hasattr(self.ui, 'lineEditShowFormat'):
                    self._set_table_item(current_row, 4, self.ui.lineEditShowFormat.text())
                if hasattr(self.ui, 'comboBoxShowFileType'):
                    self._set_table_item(current_row, 5, self.ui.comboBoxShowFileType.currentText())
                if hasattr(self.ui, 'lineEditShowFile'):
                    self._set_table_item(current_row, 6, self.ui.lineEditShowFile.text())
                    
        except Exception as e:
            self.logger_manager.log_error("从编辑器更新表格失败", "error", e)
    
    def _load_row_to_editor(self, row: int):
        """加载行数据到编辑器"""
        try:
            table = self.ui.tableWidget_2
            
            if hasattr(self.ui, 'comboBoxShowCOM'):
                self.ui.comboBoxShowCOM.setCurrentText(self._get_table_item_text(table, row, 1))
            if hasattr(self.ui, 'lineEditShowName'):
                self.ui.lineEditShowName.setText(self._get_table_item_text(table, row, 2))
            if hasattr(self.ui, 'lineEditShowText'):
                self.ui.lineEditShowText.setText(self._get_table_item_text(table, row, 3))
            if hasattr(self.ui, 'lineEditShowFormat'):
                self.ui.lineEditShowFormat.setText(self._get_table_item_text(table, row, 4))
            if hasattr(self.ui, 'comboBoxShowFileType'):
                self.ui.comboBoxShowFileType.setCurrentText(self._get_table_item_text(table, row, 5))
            if hasattr(self.ui, 'lineEditShowFile'):
                self.ui.lineEditShowFile.setText(self._get_table_item_text(table, row, 6))
                
        except Exception as e:
            self.logger_manager.log_error(f"加载行数据到编辑器失败: row={row}", "error", e)
    
    def _disconnect_editor_signals(self):
        """断开编辑器信号"""
        try:
            if hasattr(self.ui, 'lineEditShowName'):
                self.ui.lineEditShowName.textChanged.disconnect()
            if hasattr(self.ui, 'lineEditShowText'):
                self.ui.lineEditShowText.textChanged.disconnect()
            if hasattr(self.ui, 'comboBoxShowCOM'):
                self.ui.comboBoxShowCOM.currentTextChanged.disconnect()
            if hasattr(self.ui, 'lineEditShowFormat'):
                self.ui.lineEditShowFormat.textChanged.disconnect()
            if hasattr(self.ui, 'comboBoxShowFileType'):
                self.ui.comboBoxShowFileType.currentTextChanged.disconnect()
            if hasattr(self.ui, 'lineEditShowFile'):
                self.ui.lineEditShowFile.textChanged.disconnect()
        except:
            pass
    
    def _get_table_item_text(self, table, row: int, col: int) -> str:
        """获取表格项文本"""
        item = table.item(row, col)
        return item.text() if item else ""
    
    def _set_table_item(self, row: int, col: int, text: str):
        """设置表格项"""
        table = self.ui.tableWidget_2
        item = table.item(row, col)
        if item:
            item.setText(text)
        else:
            table.setItem(row, col, QTableWidgetItem(text))
    
    def load_test_data_to_table(self, test_data: Dict):
        """加载测试数据到pageSet界面的tableWidget_2表格"""
        try:
            table = self.ui.tableWidget_2
            table.setRowCount(0)
            
            for i, row_data in test_data.items():
                table.insertRow(i)
                table.setItem(i, 0, QTableWidgetItem(row_data.get("序号", str(i + 1))))
                table.setItem(i, 1, QTableWidgetItem(row_data.get("COM口", "")))
                table.setItem(i, 2, QTableWidgetItem(row_data.get("名称", "")))
                table.setItem(i, 3, QTableWidgetItem(row_data.get("内容", "")))
                table.setItem(i, 4, QTableWidgetItem(row_data.get("规格", "")))
                table.setItem(i, 5, QTableWidgetItem(row_data.get("文件类型", "")))
                table.setItem(i, 6, QTableWidgetItem(row_data.get("文件路径", "")))
            
            self.test_data = test_data
            self.logger_manager.log_info(f"加载测试数据到pageSet表格完成，共 {len(test_data)} 行")
            
        except Exception as e:
            self.logger_manager.log_error("加载测试数据到pageSet表格失败", "error", e)
    
    def collect_test_data_from_table(self) -> Dict:
        """从表格收集测试数据"""
        try:
            # 根据当前页面选择正确的表格
            current_page_index = self.ui.stackedWidget_2.currentIndex()
            
            if current_page_index == 1:  # pageSet界面
                table = self.ui.tableWidget_2
                self.logger_manager.log_debug("从pageSet的tableWidget_2收集测试数据")
            else:  # pageTest界面或其他界面，使用测试表格
                table = getattr(self.ui, 'widgetShowTable', None)
                if table is None:
                    table = getattr(self.ui, 'tableViewShowTable', None)
                if table is None:
                    self.logger_manager.log_error("未找到测试表格组件")
                    return {}
                self.logger_manager.log_debug("从pageTest的widgetShowTable收集测试数据")
            
            test_data = {}
            
            for row in range(table.rowCount()):
                test_data[row] = {
                    "序号": self._get_table_item_text(table, row, 0),
                    "COM口": self._get_table_item_text(table, row, 1),
                    "名称": self._get_table_item_text(table, row, 2),
                    "内容": self._get_table_item_text(table, row, 3),
                    "规格": self._get_table_item_text(table, row, 4),
                    "文件类型": self._get_table_item_text(table, row, 5),
                    "文件路径": self._get_table_item_text(table, row, 6)
                }
            
            self.test_data = test_data
            self.logger_manager.log_info(f"从表格收集测试数据完成，共 {len(test_data)} 行")
            return test_data
            
        except Exception as e:
            self.logger_manager.log_error("从表格收集测试数据失败", "error", e)
            return {}
    
    def set_test_start_callback(self, callback: Callable):
        """设置测试开始回调函数"""
        self.test_start_callback = callback
    
    def update_test_status(self, status: str, row: int = -1, color: str = "white"):
        """
        更新测试状态显示
        
        Args:
            status: 状态文本
            row: 表格行号，-1表示不更新行颜色
            color: 行颜色
        """
        try:
            # 更新状态标签（如果存在）
            if hasattr(self.ui, 'labelTestStatus'):
                self.ui.labelTestStatus.setText(status)
            
            # 更新表格行颜色
            if row >= 0:
                self.set_table_row_color(row, color)
                
        except Exception as e:
            self.logger_manager.log_error("更新测试状态失败", "error", e)
    
    def set_table_row_color(self, row: int, color: str):
        """设置表格行颜色（仅用于pageTest界面的测试表格）"""
        try:
            # 只修改pageTest界面的测试表格颜色
            table = getattr(self.ui, 'widgetShowTable', None)
            if table is None:
                table = getattr(self.ui, 'tableViewShowTable', None)
            
            if table is None:
                self.logger_manager.log_warning("未找到测试表格组件，无法设置行颜色")
                return
            
            color_map = {
                "yellow": QtGui.QColor(255, 255, 0),
                "green": QtGui.QColor(0, 255, 0),
                "red": QtGui.QColor(255, 0, 0),
                "white": QtGui.QColor(255, 255, 255)
            }
            
            if color in color_map:
                bg_color = color_map[color]
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(bg_color)
            
            # 同时更新labelShow的颜色
            # 将颜色映射到状态
            status_map = {
                "green": "pass",
                "yellow": "running",
                "red": "fail",
                "white": "idle"
            }
            self.update_label_show_color(status_map.get(color, "idle"))
            self.logger_manager.log_debug(f"设置测试表格行 {row} 颜色为 {color}")
                        
        except Exception as e:
            self.logger_manager.log_error(f"设置表格行颜色失败: row={row}, color={color}", "error", e)
    
    def refresh_com_ports(self):
        """刷新pageSet界面的COM端口下拉框，与产品接口保持一致"""
        try:
            if hasattr(self.ui, 'comboBoxShowCOM'):
                current_text = self.ui.comboBoxShowCOM.currentText()
                self.ui.comboBoxShowCOM.clear()
                
                # 从硬件表格中获取产品接口列表
                product_interfaces = self._get_product_interfaces_from_table()
                
                # 如果表格为空，从配置管理器获取
                if not product_interfaces:
                    hardware_list = self.config_manager.get_hardware_list()
                    product_interfaces = [item.get("name", "") for item in hardware_list if item.get("name")]
                
                self.ui.comboBoxShowCOM.addItems([""] + product_interfaces)
                
                # 恢复之前的选择
                index = self.ui.comboBoxShowCOM.findText(current_text)
                if index >= 0:
                    self.ui.comboBoxShowCOM.setCurrentIndex(index)
                    
        except Exception as e:
            self.logger_manager.log_error("刷新COM端口失败", "error", e)
    
    def _get_product_interfaces_from_table(self) -> List[str]:
        """从硬件表格中获取产品接口列表"""
        try:
            product_interfaces = []
            table = self.ui.tableHardware
            
            for row in range(table.rowCount()):
                item = table.item(row, 4)  # 产品接口列
                if item and item.text().strip():
                    product_interfaces.append(item.text().strip())
            
            return product_interfaces
            
        except Exception as e:
            self.logger_manager.log_error("从硬件表格获取产品接口列表失败", "error", e)
            return []
    
    def refresh_hardware_com_ports_manually(self):
        """手动刷新硬件COM端口（响应用户点击）"""
        try:
            self._refresh_hardware_com_ports()
            self.show_message("成功", "COM端口列表已刷新", "info")
            self.logger_manager.log_info("手动刷新硬件COM端口列表")
        except Exception as e:
            self.logger_manager.log_error("手动刷新硬件COM端口失败", "error", e)
            self.show_message("错误", "刷新COM端口失败", "error")
    
    def _switch_to_product_page(self):
        """切换到产品测试页面并刷新界面"""
        try:
            # 跳转到产品测试页面
            self.ui.stackedWidget_2.setCurrentIndex(0)
            
            # 刷新产品测试页面到初始状态
            self._refresh_product_page()
            
            self.logger_manager.log_info("切换到产品测试页面并刷新界面")
            
        except Exception as e:
            self.logger_manager.log_error("切换到产品测试页面失败", "error", e)
    
    def _switch_to_program_page(self):
        """切换到程序编辑页面并刷新界面"""
        try:
            # 跳转到程序编辑页面
            self.ui.stackedWidget_2.setCurrentIndex(1)
            
            # 刷新程序编辑页面到初始状态
            self._refresh_program_page()
            
            self.logger_manager.log_info("切换到程序编辑页面并刷新界面")
            
        except Exception as e:
            self.logger_manager.log_error("切换到程序编辑页面失败", "error", e)
    
    def _refresh_product_page(self):
        """刷新产品测试页面到初始状态"""
        try:
            # 清空测试结果显示，并重置labelShow
            self.clear_test_results(reset_label=True)
            
            # 重置状态标签颜色（确保重置）
            self.update_label_show_color("idle")
            
            # 清空测试表格（如果存在）
            if hasattr(self.ui, 'widgetShowTable'):
                table = getattr(self.ui, 'widgetShowTable', None)
                if table and hasattr(table, 'setRowCount'):
                    table.setRowCount(0)
            
            # 重置测试信息显示
            if hasattr(self.ui, 'textEditShowInfo'):
                self.ui.textEditShowInfo.clear()
                info_text = "=== 产品测试界面 ===\n\n"
                info_text += "请先加载测试程序，然后开始测试。\n"
                info_text += "状态: 就绪\n"
                self.ui.textEditShowInfo.setPlainText(info_text)
            
            self.logger_manager.log_info("产品测试页面已刷新到初始状态")
            
        except Exception as e:
            self.logger_manager.log_error("刷新产品测试页面失败", "error", e)
    
    def _refresh_program_page(self):
        """刷新程序编辑页面到初始状态"""
        try:
            # 清空测试表格
            if hasattr(self.ui, 'tableWidget_2'):
                self.ui.tableWidget_2.setRowCount(0)
                # 重新初始化表格
                self._init_test_table()
            
            # 清空编辑器控件
            self._clear_editor_controls()
            
            # 刷新COM端口下拉框
            self.refresh_com_ports()
            
            # 取消表格选择
            if hasattr(self.ui, 'tableWidget_2'):
                self.ui.tableWidget_2.clearSelection()
            
            # 取消命令树选择
            if hasattr(self.ui, 'treeCMD'):
                self.ui.treeCMD.clearSelection()
            
            self.logger_manager.log_info("程序编辑页面已刷新到初始状态")
            
        except Exception as e:
            self.logger_manager.log_error("刷新程序编辑页面失败", "error", e)
    
    def _clear_editor_controls(self):
        """清空编辑器控件内容"""
        try:
            # 清空pageSet界面的编辑控件
            if hasattr(self.ui, 'lineEditShowName'):
                self.ui.lineEditShowName.clear()
            if hasattr(self.ui, 'lineEditShowText'):
                self.ui.lineEditShowText.clear()
            if hasattr(self.ui, 'comboBoxShowCOM'):
                self.ui.comboBoxShowCOM.setCurrentIndex(0)
            if hasattr(self.ui, 'lineEditShowFormat'):
                self.ui.lineEditShowFormat.clear()
            if hasattr(self.ui, 'comboBoxShowFileType'):
                self.ui.comboBoxShowFileType.setCurrentIndex(0)
            if hasattr(self.ui, 'lineEditShowFile'):
                self.ui.lineEditShowFile.clear()
                
        except Exception as e:
            self.logger_manager.log_error("清空编辑器控件失败", "error", e) 