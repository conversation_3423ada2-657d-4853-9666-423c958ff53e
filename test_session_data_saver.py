#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试级别数据保存功能测试脚本
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from function.data_saver import DataSaver
from function.logger_manager import <PERSON><PERSON>Mana<PERSON>


def test_session_data_saver():
    """测试测试级别数据保存功能"""
    print("开始测试测试级别数据保存功能...")
    
    # 初始化日志管理器和数据保存器
    logger_manager = LoggerManager()
    data_saver = DataSaver(logger_manager)
    
    print(f"数据保存目录: {data_saver.base_save_dir}")
    print(f"支持的指令: {data_saver.supported_commands}")
    
    # 测试1: 启动测试会话
    print("\n=== 测试1: 启动测试会话 ===")
    test_info = {
        "barcode": "TEST123456",
        "internal_sn": "HW12345678901234567",
        "mode": "自动"
    }
    session_id = data_saver.start_test_session("test_session_001", test_info)
    print(f"✓ 测试会话已启动: {session_id}")
    
    # 测试2: 添加各种指令数据
    print("\n=== 测试2: 添加指令数据到会话 ===")
    
    # RCU序列号数据
    rcu_data = {
        "valid": True,
        "serial_number": "HW12345678901234567"
    }
    result = data_saver.save_command_response_data("回读RCU序列号", rcu_data)
    print(f"RCU序列号数据: {result}")
    
    # 备份序列号数据
    backup_data = {
        "valid": True,
        "backup_serial_number": "BK98765432109876543"
    }
    result = data_saver.save_command_response_data("回读备份序列号", backup_data)
    print(f"备份序列号数据: {result}")
    
    # 电子标签数据
    tag_data = {
        "valid": True,
        "frame_num": 0,
        "tag_content": "RFID_TAG_001",
        "tag_data": b"RFID_TAG_001",
        "data_length": 12
    }
    result = data_saver.save_command_response_data("回读电子标签", tag_data)
    print(f"电子标签数据: {result}")
    
    # 软硬件版本信息
    version_data = {
        "valid": True,
        "ascii_data": "V1.0.0_20231201",
        "version_info": "V1.0.0_20231201"
    }
    result = data_saver.save_command_response_data("查询软硬件版本信息", version_data)
    print(f"软硬件版本信息: {result}")
    
    # 电机转速数据
    motor_data = {
        "valid": True,
        "motor_speed": 128
    }
    result = data_saver.save_command_response_data("读取电机转速", motor_data)
    print(f"电机转速数据: {result}")
    
    # 电压值数据
    voltage_data = {
        "success": True,
        "raw_response": "12.50",
        "parsed_data": {
            "voltage": 12.50
        }
    }
    result = data_saver.save_command_response_data("读取电压值", voltage_data)
    print(f"电压值数据: {result}")
    
    # 电流值数据
    current_data = {
        "success": True,
        "raw_response": "2.35",
        "parsed_data": {
            "current": 2.35
        }
    }
    result = data_saver.save_command_response_data("读取电流值", current_data)
    print(f"电流值数据: {result}")
    
    # 测试3: 结束测试会话并保存
    print("\n=== 测试3: 结束测试会话 ===")
    saved_path = data_saver.end_test_session()
    if saved_path:
        print(f"✓ 测试会话数据保存成功: {saved_path}")
        
        # 验证文件内容
        print("\n=== 文件内容验证 ===")
        with open(saved_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print("保存的文件内容:")
            print("-" * 60)
            print(content)
            print("-" * 60)
    else:
        print("✗ 测试会话数据保存失败")
    
    # 测试4: 测试合路器信息文件（仍然单独保存）
    print("\n=== 测试4: 合路器信息文件（单独保存） ===")
    
    # 启动新的测试会话
    session_id2 = data_saver.start_test_session("test_session_002", {"barcode": "TEST789"})
    
    # 模拟合路器信息文件多帧数据
    frame0_data = {
        "valid": True,
        "file_type": "模块信息文件",
        "frame_num": 0,
        "file_data": bytes.fromhex("48656C6C6F20576F726C64" * 6),  # 足够大的数据
        "data_size": 66
    }
    result = data_saver.save_command_response_data("回读合路器信息文件", frame0_data, session_id="combiner_test")
    print(f"合路器文件帧0: {result}")
    
    # 最后一帧
    frame1_data = {
        "valid": True,
        "file_type": "模块信息文件",
        "frame_num": 1,
        "file_data": bytes.fromhex("454E44"),  # "END"
        "data_size": 3
    }
    result = data_saver.save_command_response_data("回读合路器信息文件", frame1_data, 
                                                  session_id="combiner_test", is_last_frame=True)
    print(f"合路器文件帧1: {result}")
    
    # 结束第二个会话
    saved_path2 = data_saver.end_test_session()
    print(f"第二个会话保存结果: {saved_path2}")
    
    # 测试5: 测试没有会话时的回退行为
    print("\n=== 测试5: 无会话时的回退行为 ===")
    rcu_data_fallback = {
        "valid": True,
        "serial_number": "FALLBACK_SN_123"
    }
    result = data_saver.save_command_response_data("回读RCU序列号", rcu_data_fallback)
    print(f"无会话时RCU序列号保存: {result}")
    
    print("\n=== 测试完成 ===")
    print(f"保存目录: {data_saver.base_save_dir}")
    print("请检查保存的文件内容是否正确。")


if __name__ == "__main__":
    test_session_data_saver()
