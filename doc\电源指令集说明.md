# 电源指令集说明文档

## 概述

本文档描述了新增的电源控制指令集，用于控制和监控电源设备。电源指令集使用ASCII格式，与现有的二进制指令系统并行工作。

## 指令格式

### 基本格式
- **数据格式**: ASCII字符串
- **结尾符**: 换行符 (0x0D 0x0A, 即 \r\n)
- **校验**: 无需CRC校验
- **编码**: ASCII编码

### 指令类型

#### 1. 查询上位机状态指令集
**指令名称**: `查询上位机状态`

**指令序列**:
```
REMOTE\r\n
OUT0\r\n
VSET1:12\r\n
VSET2:12\r\n
ISET1:2\r\n
ISET2:2\r\n
*IDN?\r\n
```

**功能**: 查询设备状态并设置初始参数
**验证**: 当收到最后一条`*IDN?`指令的设备信息响应时，表示指令集发送成功

#### 2. 切换供电开关指令
**指令名称**: `切换供电开关`
**指令内容**: `OUT1\r\n`
**功能**: 切换电源输出开关
**响应**: 无需特殊响应验证

#### 3. 查询供电开关指令
**指令名称**: `查询供电开关`
**指令内容**: `STATUS\r\n`
**功能**: 查询当前供电开关状态
**响应格式**: 8位二进制数据

**响应数据解析**:
- 第0-1位: 通道模式 (1=恒压模式, 0=恒流模式)
- 第2-3位: 电源模式 (00/01=独立, 11=串联, 10=并联)
- 第4位: 蜂鸣器开关 (0=关, 1=开)
- 第5位: 输出开关 (0=关, 1=开)
- 第6-7位: 波特率 (00=115200bps, 01=57600bps, 10=9600bps)

#### 4. 读取电流值指令
**指令名称**: `读取电流值`
**指令格式**: `ISET<X>?\r\n`
**参数**: 
- `port`: 端口号，由用户输入决定

**示例**:
- 端口1: `ISET1?\r\n`
- 端口2: `ISET2?\r\n`

**响应格式**: `x.xxxA\r\n` (ASCII格式的电流值)

#### 5. 读取电压值指令
**指令名称**: `读取电压值`
**指令格式**: `VSET<X>?\r\n`
**参数**:
- `port`: 端口号，由用户输入决定

**示例**:
- 端口1: `VSET1?\r\n`
- 端口2: `VSET2?\r\n`

**响应格式**: `xx.xxxV\r\n` 或 `x.xxxV\r\n` (ASCII格式的电压值)

## 校准期间特殊读取逻辑

### 校准模式
当系统进入校准模式后，电压和电流读取指令会启用特殊的读取逻辑：

#### 基本校准读取
- **校准时间**: 30秒
- **读取次数**: 100次
- **结果**: 取最大值显示

#### 交替读取模式
当电压和电流指令连续出现时：
- **执行方式**: 交替执行
- **读取次数**: 电压100次，电流100次
- **结果**: 分别取各自的最大值

#### 非阻塞实现
为避免影响校准过程，读取实现采用非阻塞方式：
- 移除了传统的`sleep`延时
- 使用较短的超时时间(0.1秒)
- 快速连续读取，不影响校准精度

### 校准API

#### 开始校准
```python
command_handler.power_handler.start_calibration()
```

#### 停止校准
```python
command_handler.power_handler.stop_calibration()
```

#### 设置交替模式
```python
command_handler.power_handler.set_alternating_mode(True)  # 启用
command_handler.power_handler.set_alternating_mode(False) # 禁用
```

#### 执行校准读取
```python
result = command_handler.power_handler.execute_calibration_reading(
    command_name="读取电压值", 
    port="1", 
    serial_manager=serial_manager
)
```

## 集成方式

### CommandHandler集成
电源指令已集成到主`CommandHandler`类中：

```python
# 生成电源指令
cmd = command_handler.generate_command("读取电压值", port="1")

# 验证电源响应
is_valid = command_handler.validate_response("读取电压值", response)

# 获取所有指令（包括电源指令）
all_commands = command_handler.get_all_commands()
```

### SerialManager支持
串口管理器已支持ASCII格式数据：

```python
# 发送电源指令
success = serial_manager.send_power_command(cmd_bytes)

# 接收电源响应
response = serial_manager.receive_power_response(timeout=2.0)

# 发送指令序列
responses = serial_manager.send_power_command_sequence(commands)
```

## 使用示例

### 基本使用
```python
from function.command_handler import CommandHandler
from function.serial_manager import SerialManager

# 初始化
command_handler = CommandHandler(logger_manager)
serial_manager = SerialManager(logger_manager)

# 1. 查询上位机状态
status_cmd = command_handler.generate_command("查询上位机状态")
serial_manager.send_power_command(status_cmd)
response = serial_manager.receive_power_response()

# 2. 读取电压值
voltage_cmd = command_handler.generate_command("读取电压值", port="1")
serial_manager.send_power_command(voltage_cmd)
voltage_response = serial_manager.receive_power_response()

# 解析响应
parsed = command_handler.power_handler.parse_power_response(
    voltage_response, "读取电压值"
)
if parsed["success"]:
    voltage_value = parsed["parsed_data"]["voltage"]
    print(f"电压值: {voltage_value}V")
```

### 校准期间使用
```python
# 开始校准
command_handler.power_handler.start_calibration()

# 设置交替模式（如果需要同时读取电压和电流）
command_handler.power_handler.set_alternating_mode(True)

# 执行校准读取
result = command_handler.power_handler.execute_calibration_reading(
    "读取电压值", "1", serial_manager
)

if result["success"]:
    print(f"校准完成，最大电压值: {result['max_value']}V")

# 停止校准
command_handler.power_handler.stop_calibration()
```

## 错误处理

### 常见错误
1. **指令生成失败**: 返回`None`
2. **响应解析失败**: 返回包含错误信息的字典
3. **校准超时**: 在30秒内自动停止

### 错误检查
```python
# 检查指令生成
cmd = command_handler.generate_command("读取电压值", port="1")
if cmd is None:
    print("指令生成失败")

# 检查响应解析
result = command_handler.power_handler.parse_power_response(response, "读取电压值")
if not result["success"]:
    print(f"解析失败: {result['message']}")
```

## 注意事项

1. **数据格式**: 电源指令使用ASCII格式，与现有二进制指令不同
2. **换行符**: 所有指令和响应都以`\r\n`结尾
3. **校验**: 电源指令不需要CRC校验
4. **校准影响**: 校准期间的读取使用非阻塞方式，避免影响校准精度
5. **端口参数**: 电压和电流读取指令需要指定端口参数
6. **响应验证**: 不同指令有不同的响应验证逻辑

## 测试

项目包含完整的测试用例：
- `test_power_commands.py`: 单元测试
- `test_power_integration.py`: 集成测试

运行测试：
```bash
python test_power_commands.py
python test_power_integration.py
```
实际运行中没有出现在校准期间打开电源接口读取电压电流值的情况，应该是程序中起了冲突或者是必须等待天线校准结束之后才能读取电压电流值。程序的实现逻辑有问题。我希望实现的是，读取天线校准期间的最大电压值与电流值与电机转速。目前已知信息是读取电压值与电流值采用的是电源接口，读取电机转速采用的是RCU接口，天线校准时间固定为32s。你想一个办法来实现我想要的功能，并且不要和以前其他指令的程序产生冲突。


目前已知信息是读取电压值与电流值采用的是电源AISGIN接口，读取电机转速采用的是AISGIN接口，天线校准时间固定为32s。读取表格指令集中的单行指令时，若该行指令是天线校准命令指令，则查看后3行指令，看是否需要在校准期间读取电压值，电流值或电机转速。这三个数据的读取使用读取电压值，读取电流值，读取电机转速三个指令实现。按照后3行是否出现了对应的指令，在校准时间内按一定次数读取对应数据。天线校准结束后将多次读取的数据的最大值显示在状态区。天线校准指令结束后，记得发送响应帧。

串口数据似乎有些问题，在测试的后期，响应帧发送后还没有接收到回传，就发送了新指令。具体可查看serial.log文件中的第36106行后的内容。另外，我在执行test2.xml文件的指令中，它跳过了第10行的天线校准指令以及后续的电机转速和设置天线倾角指令，还有，因为有两个电机，所以监控模式下读取电机转速时，发送的指令一定要根据xml文件中content列决定。最后，优化一下显示，电流值和电压值以及监控数据的内容采用ASCII码显示。

你的更改似乎把一些正确的操作给跳过了，例如读取电机转速时，如果遇到下位机回传的数据是中间帧前置，应该发送中间帧而不是跳过不管直接发送响应帧。这样操作，我很好奇你收到中间帧是怎么解析出来电机转速的。