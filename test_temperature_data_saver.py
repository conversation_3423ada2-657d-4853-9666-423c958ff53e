#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温度信息数据保存功能测试脚本
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from function.data_saver import DataSaver
from function.logger_manager import <PERSON><PERSON><PERSON>ana<PERSON>


def test_temperature_data_saver():
    """测试温度信息数据保存功能"""
    print("开始测试温度信息数据保存功能...")
    
    # 初始化日志管理器和数据保存器
    logger_manager = LoggerManager()
    data_saver = DataSaver(logger_manager)
    
    print(f"数据保存目录: {data_saver.base_save_dir}")
    print(f"支持的指令: {data_saver.supported_commands}")
    
    # 测试1: 测试级别保存 - 启动测试会话
    print("\n=== 测试1: 测试级别保存 ===")
    test_info = {
        "barcode": "TEMP_TEST_001",
        "internal_sn": "HW12345678901234567",
        "mode": "自动"
    }
    session_id = data_saver.start_test_session("temp_test_session", test_info)
    print(f"✓ 测试会话已启动: {session_id}")
    
    # 添加温度信息数据到会话
    temp_data_normal = {
        "valid": True,
        "temperature": 25  # 正常温度 (0x19)
    }
    result = data_saver.save_command_response_data("回读温度信息", temp_data_normal)
    print(f"正常温度数据保存结果: {result}")
    
    # 添加异常温度数据
    temp_data_abnormal = {
        "valid": True,
        "temperature": 35  # 异常温度
    }
    result = data_saver.save_command_response_data("回读温度信息", temp_data_abnormal)
    print(f"异常温度数据保存结果: {result}")
    
    # 添加负温度数据
    temp_data_negative = {
        "valid": True,
        "temperature": -10  # 负温度
    }
    result = data_saver.save_command_response_data("回读温度信息", temp_data_negative)
    print(f"负温度数据保存结果: {result}")
    
    # 结束测试会话
    saved_path = data_saver.end_test_session()
    if saved_path:
        print(f"✓ 测试会话数据保存成功: {saved_path}")
        
        # 验证文件内容
        print("\n=== 测试级别文件内容验证 ===")
        with open(saved_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print("保存的文件内容:")
            print("-" * 60)
            print(content)
            print("-" * 60)
    else:
        print("✗ 测试会话数据保存失败")
    
    # 测试2: 单独保存（回退方案）
    print("\n=== 测试2: 单独保存（回退方案） ===")
    
    # 测试正常温度
    temp_data_single = {
        "valid": True,
        "temperature": 28
    }
    saved_path_single = data_saver.save_command_response_data("回读温度信息", temp_data_single)
    if saved_path_single and saved_path_single.endswith('.txt'):
        print(f"✓ 单独温度数据保存成功: {saved_path_single}")
        
        # 验证单独文件内容
        print("\n=== 单独文件内容验证 ===")
        with open(saved_path_single, 'r', encoding='utf-8') as f:
            content = f.read()
            print("单独保存的文件内容:")
            print("-" * 40)
            print(content)
            print("-" * 40)
    else:
        print("✗ 单独温度数据保存失败")
    
    # 测试3: 无效数据测试
    print("\n=== 测试3: 无效数据测试 ===")
    invalid_temp_data = {
        "valid": False,
        "temperature": 25
    }
    result = data_saver.save_command_response_data("回读温度信息", invalid_temp_data)
    if result is None:
        print("✓ 正确拒绝了无效的温度数据")
    else:
        print("✗ 错误地保存了无效的温度数据")
    
    # 测试4: 缺少温度字段测试
    print("\n=== 测试4: 缺少温度字段测试 ===")
    missing_temp_data = {
        "valid": True
        # 缺少temperature字段
    }
    result = data_saver.save_command_response_data("回读温度信息", missing_temp_data)
    if result is None:
        print("✓ 正确拒绝了缺少温度字段的数据")
    else:
        print("✗ 错误地保存了缺少温度字段的数据")
    
    # 测试5: 边界值测试
    print("\n=== 测试5: 边界值测试 ===")
    
    # 启动新的测试会话用于边界值测试
    boundary_session_id = data_saver.start_test_session("boundary_test", {"test_type": "边界值测试"})
    
    # 测试各种边界温度值
    boundary_temps = [
        {"temp": 0, "desc": "零度"},
        {"temp": 22, "desc": "标准值-3°C(边界正常)"},
        {"temp": 28, "desc": "标准值+3°C(边界正常)"},
        {"temp": 21, "desc": "标准值-4°C(边界异常)"},
        {"temp": 29, "desc": "标准值+4°C(边界异常)"},
        {"temp": 100, "desc": "高温"},
        {"temp": -50, "desc": "极低温"}
    ]
    
    for temp_info in boundary_temps:
        temp_data = {
            "valid": True,
            "temperature": temp_info["temp"]
        }
        result = data_saver.save_command_response_data("回读温度信息", temp_data)
        print(f"{temp_info['desc']} ({temp_info['temp']}°C): {result}")
    
    # 结束边界值测试会话
    boundary_saved_path = data_saver.end_test_session()
    if boundary_saved_path:
        print(f"✓ 边界值测试数据保存成功: {boundary_saved_path}")
    
    print("\n=== 测试完成 ===")
    print(f"保存目录: {data_saver.base_save_dir}")
    print("请检查保存的文件内容是否正确。")


if __name__ == "__main__":
    test_temperature_data_saver()
