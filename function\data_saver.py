# -*- coding: utf-8 -*-
"""
数据保存器模块
用于保存指令响应的有效数据部分到txt文件和二进制文件
"""

import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path


class DataSaver:
    """数据保存器类"""
    
    def __init__(self, logger_manager=None):
        """
        初始化数据保存器
        
        Args:
            logger_manager: 日志管理器
        """
        self.logger_manager = logger_manager
        
        # 创建保存目录
        self.base_save_dir = "saved_data"
        self.txt_save_dir = os.path.join(self.base_save_dir, "txt_files")
        self.bin_save_dir = os.path.join(self.base_save_dir, "binary_files")
        
        # 确保目录存在
        self._ensure_directories()
        
        # 合路器信息文件的数据缓存
        self.combiner_data_cache = {}
        
        # 支持保存的指令列表
        self.supported_commands = {
            "回读合路器信息文件",
            "回读RCU序列号", 
            "回读备份序列号",
            "查询软硬件版本信息",
            "读取电机转速",
            "读取电压值",
            "读取电流值"
        }
    
    def _ensure_directories(self):
        """确保保存目录存在"""
        try:
            os.makedirs(self.txt_save_dir, exist_ok=True)
            os.makedirs(self.bin_save_dir, exist_ok=True)
            self._log_info(f"数据保存目录已创建: {self.base_save_dir}")
        except Exception as e:
            self._log_error(f"创建保存目录失败: {e}")
    
    def _log_info(self, message: str):
        """记录信息日志"""
        if self.logger_manager:
            self.logger_manager.log_info(message)
        else:
            print(f"INFO: {message}")
    
    def _log_error(self, message: str):
        """记录错误日志"""
        if self.logger_manager:
            self.logger_manager.log_error(message)
        else:
            print(f"ERROR: {message}")
    
    def _log_debug(self, message: str):
        """记录调试日志"""
        if self.logger_manager:
            self.logger_manager.log_debug(message)
        else:
            print(f"DEBUG: {message}")
    
    def _generate_timestamp_filename(self, command_name: str, extension: str = "txt") -> str:
        """
        生成带时间戳的文件名
        
        Args:
            command_name: 指令名称
            extension: 文件扩展名
            
        Returns:
            str: 文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_command_name = command_name.replace(" ", "_").replace("/", "_")
        return f"{safe_command_name}_{timestamp}.{extension}"
    
    def should_save_data(self, command_name: str) -> bool:
        """
        检查是否应该保存该指令的数据
        
        Args:
            command_name: 指令名称
            
        Returns:
            bool: 是否应该保存
        """
        return command_name in self.supported_commands
    
    def save_command_response_data(self, command_name: str, response_data: Dict, **kwargs) -> Optional[str]:
        """
        保存指令响应数据
        
        Args:
            command_name: 指令名称
            response_data: 解析后的响应数据
            **kwargs: 额外参数
            
        Returns:
            str: 保存的文件路径，失败返回None
        """
        if not self.should_save_data(command_name):
            return None
        
        try:
            if command_name == "回读合路器信息文件":
                return self._save_combiner_info_data(response_data, **kwargs)
            elif command_name == "回读RCU序列号":
                return self._save_rcu_serial_data(response_data)
            elif command_name == "回读备份序列号":
                return self._save_backup_serial_data(response_data)
            elif command_name == "查询软硬件版本信息":
                return self._save_version_info_data(response_data)
            elif command_name == "读取电机转速":
                return self._save_motor_speed_data(response_data)
            elif command_name in ["读取电压值", "读取电流值"]:
                return self._save_power_data(command_name, response_data)
            else:
                self._log_debug(f"未处理的指令类型: {command_name}")
                return None
                
        except Exception as e:
            self._log_error(f"保存指令响应数据失败 - {command_name}: {e}")
            return None
    
    def _save_rcu_serial_data(self, response_data: Dict) -> Optional[str]:
        """
        保存RCU序列号数据
        
        Args:
            response_data: 响应数据
            
        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "serial_number" not in response_data:
                return None
            
            serial_number = response_data["serial_number"]
            
            # 生成文件名
            filename = self._generate_timestamp_filename("回读RCU序列号")
            filepath = os.path.join(self.txt_save_dir, filename)
            
            # 保存到txt文件
            content = f"RCU序列号: {serial_number}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self._log_info(f"RCU序列号数据已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self._log_error(f"保存RCU序列号数据失败: {e}")
            return None
    
    def _save_backup_serial_data(self, response_data: Dict) -> Optional[str]:
        """
        保存备份序列号数据
        
        Args:
            response_data: 响应数据
            
        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "backup_serial_number" not in response_data:
                return None
            
            backup_serial_number = response_data["backup_serial_number"]
            
            # 生成文件名
            filename = self._generate_timestamp_filename("回读备份序列号")
            filepath = os.path.join(self.txt_save_dir, filename)
            
            # 保存到txt文件
            content = f"备份序列号: {backup_serial_number}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self._log_info(f"备份序列号数据已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self._log_error(f"保存备份序列号数据失败: {e}")
            return None

    def _save_version_info_data(self, response_data: Dict) -> Optional[str]:
        """
        保存软硬件版本信息数据

        Args:
            response_data: 响应数据

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid"):
                return None

            # 生成文件名
            filename = self._generate_timestamp_filename("查询软硬件版本信息")
            filepath = os.path.join(self.txt_save_dir, filename)

            # 保存到txt文件
            content = f"软硬件版本信息:\n"

            # 添加所有可用的版本信息
            for key, value in response_data.items():
                if key != "valid" and value is not None:
                    content += f"{key}: {value}\n"

            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"软硬件版本信息数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存软硬件版本信息数据失败: {e}")
            return None

    def _save_motor_speed_data(self, response_data: Dict) -> Optional[str]:
        """
        保存电机转速数据

        Args:
            response_data: 响应数据

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "motor_speed" not in response_data:
                return None

            motor_speed = response_data["motor_speed"]

            # 生成文件名
            filename = self._generate_timestamp_filename("读取电机转速")
            filepath = os.path.join(self.txt_save_dir, filename)

            # 保存到txt文件
            content = f"电机转速: {motor_speed}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"电机转速数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存电机转速数据失败: {e}")
            return None

    def _save_power_data(self, command_name: str, response_data: Dict) -> Optional[str]:
        """
        保存电源数据（电压值或电流值）

        Args:
            command_name: 指令名称
            response_data: 响应数据

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("success") or not response_data.get("parsed_data"):
                return None

            parsed_data = response_data["parsed_data"]

            # 生成文件名
            filename = self._generate_timestamp_filename(command_name)
            filepath = os.path.join(self.txt_save_dir, filename)

            # 保存到txt文件
            content = f"{command_name}数据:\n"

            if command_name == "读取电压值" and "voltage" in parsed_data:
                content += f"电压值: {parsed_data['voltage']}V\n"
            elif command_name == "读取电流值" and "current" in parsed_data:
                content += f"电流值: {parsed_data['current']}A\n"

            # 添加原始响应
            if "raw_response" in response_data:
                content += f"原始响应: {response_data['raw_response']}\n"

            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"{command_name}数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存{command_name}数据失败: {e}")
            return None

    def _save_combiner_info_data(self, response_data: Dict, **kwargs) -> Optional[str]:
        """
        保存合路器信息文件数据

        Args:
            response_data: 响应数据
            **kwargs: 额外参数，包括file_type, frame_num等

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "file_data" not in response_data:
                return None

            file_type = response_data.get("file_type", "未知类型")
            frame_num = response_data.get("frame_num", 0)
            file_data = response_data["file_data"]

            # 获取缓存键
            cache_key = f"{file_type}_{kwargs.get('session_id', 'default')}"

            # 初始化缓存
            if cache_key not in self.combiner_data_cache:
                self.combiner_data_cache[cache_key] = {
                    "file_type": file_type,
                    "frames": {},
                    "total_data": bytearray(),
                    "start_time": datetime.now()
                }

            # 添加当前帧数据到缓存
            cache = self.combiner_data_cache[cache_key]
            cache["frames"][frame_num] = file_data
            cache["total_data"].extend(file_data)

            self._log_debug(f"合路器信息文件帧{frame_num}数据已缓存，数据长度: {len(file_data)}")

            # 检查是否是最后一帧（只有明确指示才认为是最后一帧）
            is_last_frame = kwargs.get("is_last_frame", False)

            if is_last_frame:
                # 保存完整的二进制文件和txt文件
                return self._finalize_combiner_info_save(cache_key)
            else:
                # 中间帧，只记录日志
                self._log_info(f"合路器信息文件帧{frame_num}数据已接收，等待更多帧...")
                # 返回一个临时状态信息，但不是最终文件路径
                return f"frame_{frame_num}_cached"

        except Exception as e:
            self._log_error(f"保存合路器信息文件数据失败: {e}")
            return None

    def _finalize_combiner_info_save(self, cache_key: str) -> Optional[str]:
        """
        完成合路器信息文件的保存

        Args:
            cache_key: 缓存键

        Returns:
            str: txt文件路径
        """
        try:
            if cache_key not in self.combiner_data_cache:
                return None

            cache = self.combiner_data_cache[cache_key]
            file_type = cache["file_type"]
            total_data = bytes(cache["total_data"])
            frames = cache["frames"]

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_file_type = file_type.replace(" ", "_").replace("/", "_")

            # 保存二进制文件
            bin_filename = f"合路器信息文件_{safe_file_type}_{timestamp}.bin"
            bin_filepath = os.path.join(self.bin_save_dir, bin_filename)

            with open(bin_filepath, 'wb') as f:
                f.write(total_data)

            # 保存txt文件（包含二进制文件路径）
            txt_filename = f"合路器信息文件_{safe_file_type}_{timestamp}.txt"
            txt_filepath = os.path.join(self.txt_save_dir, txt_filename)

            content = f"合路器信息文件数据保存记录\n"
            content += f"文件类型: {file_type}\n"
            content += f"总帧数: {len(frames)}\n"
            content += f"总数据长度: {len(total_data)} 字节\n"
            content += f"二进制文件路径: {bin_filepath}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            # 添加各帧信息
            content += "各帧详细信息:\n"
            for frame_num in sorted(frames.keys()):
                frame_data = frames[frame_num]
                content += f"帧{frame_num}: {len(frame_data)} 字节\n"
                content += f"  数据(十六进制): {frame_data.hex().upper()}\n"

            with open(txt_filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            # 清理缓存
            del self.combiner_data_cache[cache_key]

            self._log_info(f"合路器信息文件数据保存完成:")
            self._log_info(f"  二进制文件: {bin_filepath}")
            self._log_info(f"  文本文件: {txt_filepath}")

            return txt_filepath

        except Exception as e:
            self._log_error(f"完成合路器信息文件保存失败: {e}")
            return None

    def clear_combiner_cache(self, file_type: str = None, session_id: str = None):
        """
        清理合路器信息文件缓存

        Args:
            file_type: 文件类型，None表示清理所有
            session_id: 会话ID，None表示清理所有
        """
        try:
            if file_type is None and session_id is None:
                # 清理所有缓存
                self.combiner_data_cache.clear()
                self._log_info("已清理所有合路器信息文件缓存")
            else:
                # 清理特定缓存
                keys_to_remove = []
                for key in self.combiner_data_cache.keys():
                    if file_type and not key.startswith(file_type):
                        continue
                    if session_id and not key.endswith(session_id):
                        continue
                    keys_to_remove.append(key)

                for key in keys_to_remove:
                    del self.combiner_data_cache[key]

                self._log_info(f"已清理合路器信息文件缓存: {keys_to_remove}")

        except Exception as e:
            self._log_error(f"清理合路器信息文件缓存失败: {e}")
