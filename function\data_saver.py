# -*- coding: utf-8 -*-
"""
数据保存器模块
用于保存指令响应的有效数据部分到txt文件和二进制文件
"""

import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path


class DataSaver:
    """数据保存器类"""
    
    def __init__(self, logger_manager=None):
        """
        初始化数据保存器
        
        Args:
            logger_manager: 日志管理器
        """
        self.logger_manager = logger_manager
        
        # 创建保存目录
        self.base_save_dir = "saved_data"
        self.txt_save_dir = os.path.join(self.base_save_dir, "txt_files")
        self.bin_save_dir = os.path.join(self.base_save_dir, "binary_files")
        
        # 确保目录存在
        self._ensure_directories()
        
        # 合路器信息文件的数据缓存
        self.combiner_data_cache = {}

        # 测试会话数据缓存
        self.test_session_data = {}
        self.current_session_id = None

        # 支持保存的指令列表
        self.supported_commands = {
            "回读合路器信息文件",
            "回读RCU序列号",
            "回读备份序列号",
            "回读电子标签",
            "查询软硬件版本信息",
            "读取电机转速",
            "读取电压值",
            "读取电流值"
        }
    
    def _ensure_directories(self):
        """确保保存目录存在"""
        try:
            os.makedirs(self.txt_save_dir, exist_ok=True)
            os.makedirs(self.bin_save_dir, exist_ok=True)
            self._log_info(f"数据保存目录已创建: {self.base_save_dir}")
        except Exception as e:
            self._log_error(f"创建保存目录失败: {e}")
    
    def _log_info(self, message: str):
        """记录信息日志"""
        if self.logger_manager:
            self.logger_manager.log_info(message)
        else:
            print(f"INFO: {message}")
    
    def _log_error(self, message: str):
        """记录错误日志"""
        if self.logger_manager:
            self.logger_manager.log_error(message)
        else:
            print(f"ERROR: {message}")
    
    def _log_debug(self, message: str):
        """记录调试日志"""
        if self.logger_manager:
            self.logger_manager.log_debug(message)
        else:
            print(f"DEBUG: {message}")
    
    def _generate_timestamp_filename(self, command_name: str, extension: str = "txt") -> str:
        """
        生成带时间戳的文件名
        
        Args:
            command_name: 指令名称
            extension: 文件扩展名
            
        Returns:
            str: 文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_command_name = command_name.replace(" ", "_").replace("/", "_")
        return f"{safe_command_name}_{timestamp}.{extension}"
    
    def should_save_data(self, command_name: str) -> bool:
        """
        检查是否应该保存该指令的数据

        Args:
            command_name: 指令名称

        Returns:
            bool: 是否应该保存
        """
        return command_name in self.supported_commands

    def start_test_session(self, session_id: str = None, test_info: Dict = None) -> str:
        """
        开始一个新的测试会话

        Args:
            session_id: 会话ID，如果为None则自动生成
            test_info: 测试信息（如条码、序列号等）

        Returns:
            str: 会话ID
        """
        if session_id is None:
            session_id = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        self.current_session_id = session_id
        self.test_session_data[session_id] = {
            "start_time": datetime.now(),
            "test_info": test_info or {},
            "commands_data": [],
            "combiner_files": {}
        }

        self._log_info(f"开始测试会话: {session_id}")
        return session_id

    def end_test_session(self, session_id: str = None) -> Optional[str]:
        """
        结束测试会话并保存数据

        Args:
            session_id: 会话ID，如果为None则使用当前会话

        Returns:
            str: 保存的文件路径，失败返回None
        """
        if session_id is None:
            session_id = self.current_session_id

        if session_id not in self.test_session_data:
            self._log_error(f"测试会话不存在: {session_id}")
            return None

        try:
            session_data = self.test_session_data[session_id]
            saved_path = self._save_test_session_data(session_id, session_data)

            # 清理会话数据
            del self.test_session_data[session_id]
            if self.current_session_id == session_id:
                self.current_session_id = None

            self._log_info(f"测试会话结束: {session_id}")
            return saved_path

        except Exception as e:
            self._log_error(f"结束测试会话失败: {e}")
            return None
    
    def save_command_response_data(self, command_name: str, response_data: Dict, **kwargs) -> Optional[str]:
        """
        保存指令响应数据到当前测试会话

        Args:
            command_name: 指令名称
            response_data: 解析后的响应数据
            **kwargs: 额外参数

        Returns:
            str: 状态信息，测试级别保存时返回"session_cached"
        """
        if not self.should_save_data(command_name):
            return None

        try:
            # 合路器信息文件仍然单独处理（需要二进制文件）
            if command_name == "回读合路器信息文件":
                return self._save_combiner_info_data(response_data, **kwargs)

            # 其他指令添加到测试会话数据中
            if self.current_session_id and self.current_session_id in self.test_session_data:
                command_data = self._format_command_data_for_session(command_name, response_data, **kwargs)
                if command_data:
                    self.test_session_data[self.current_session_id]["commands_data"].append(command_data)
                    self._log_debug(f"指令 {command_name} 数据已添加到测试会话")
                    return "session_cached"
            else:
                # 如果没有活动的测试会话，回退到单独保存
                self._log_debug(f"没有活动的测试会话，单独保存指令 {command_name}")
                return self._save_individual_command_data(command_name, response_data, **kwargs)

        except Exception as e:
            self._log_error(f"保存指令响应数据失败 - {command_name}: {e}")
            return None

    def _format_command_data_for_session(self, command_name: str, response_data: Dict, **kwargs) -> Optional[Dict]:
        """
        格式化指令数据用于测试会话保存（ASCII码格式）

        Args:
            command_name: 指令名称
            response_data: 响应数据
            **kwargs: 额外参数

        Returns:
            dict: 格式化后的指令数据
        """
        try:
            if not response_data.get("valid", False) and not response_data.get("success", False):
                return None

            command_data = {
                "command_name": command_name,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "data_type": "ASCII",
                "content": ""
            }

            if command_name == "回读RCU序列号" and "serial_number" in response_data:
                command_data["content"] = f"RCU序列号: {response_data['serial_number']}"

            elif command_name == "回读备份序列号" and "backup_serial_number" in response_data:
                command_data["content"] = f"备份序列号: {response_data['backup_serial_number']}"

            elif command_name == "回读电子标签":
                if "tag_content" in response_data:
                    frame_num = response_data.get("frame_num", 0)
                    tag_content = response_data["tag_content"]
                    command_data["content"] = f"电子标签(帧{frame_num}): {tag_content}"
                elif "tag_data" in response_data:
                    frame_num = response_data.get("frame_num", 0)
                    tag_data = response_data["tag_data"]
                    # 尝试转换为ASCII
                    try:
                        tag_ascii = tag_data.decode('ascii', errors='ignore')
                        command_data["content"] = f"电子标签(帧{frame_num}): {tag_ascii}"
                    except:
                        command_data["content"] = f"电子标签(帧{frame_num}): {tag_data.hex().upper()}"

            elif command_name == "查询软硬件版本信息":
                if "ascii_data" in response_data:
                    command_data["content"] = f"软硬件版本信息: {response_data['ascii_data']}"
                elif "version_info" in response_data:
                    command_data["content"] = f"软硬件版本信息: {response_data['version_info']}"

            elif command_name == "读取电机转速" and "motor_speed" in response_data:
                motor_speed = response_data["motor_speed"]
                command_data["content"] = f"电机转速: {motor_speed}"

            elif command_name == "读取电压值" and response_data.get("success"):
                parsed_data = response_data.get("parsed_data", {})
                if "voltage" in parsed_data:
                    voltage = parsed_data["voltage"]
                    command_data["content"] = f"电压值: {voltage}V"
                else:
                    raw_response = response_data.get("raw_response", "")
                    command_data["content"] = f"电压值: {raw_response}"

            elif command_name == "读取电流值" and response_data.get("success"):
                parsed_data = response_data.get("parsed_data", {})
                if "current" in parsed_data:
                    current = parsed_data["current"]
                    command_data["content"] = f"电流值: {current}A"
                else:
                    raw_response = response_data.get("raw_response", "")
                    command_data["content"] = f"电流值: {raw_response}"

            return command_data if command_data["content"] else None

        except Exception as e:
            self._log_error(f"格式化指令数据失败 - {command_name}: {e}")
            return None

    def _save_test_session_data(self, session_id: str, session_data: Dict) -> Optional[str]:
        """
        保存测试会话数据到文件

        Args:
            session_id: 会话ID
            session_data: 会话数据

        Returns:
            str: 保存的文件路径
        """
        try:
            # 生成文件名
            timestamp = session_data["start_time"].strftime("%Y%m%d_%H%M%S")
            filename = f"测试数据_{session_id}_{timestamp}.txt"
            filepath = os.path.join(self.txt_save_dir, filename)

            # 构建文件内容
            content = "测试数据保存记录\n"
            content += "=" * 50 + "\n"
            content += f"会话ID: {session_id}\n"
            content += f"开始时间: {session_data['start_time'].strftime('%Y-%m-%d %H:%M:%S')}\n"
            content += f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            # 添加测试信息
            test_info = session_data.get("test_info", {})
            if test_info:
                content += "\n测试信息:\n"
                for key, value in test_info.items():
                    content += f"  {key}: {value}\n"

            # 添加指令数据
            commands_data = session_data.get("commands_data", [])
            if commands_data:
                content += f"\n指令数据记录 (共{len(commands_data)}条):\n"
                content += "-" * 50 + "\n"

                for i, cmd_data in enumerate(commands_data, 1):
                    content += f"[{i:02d}] {cmd_data['timestamp']} - {cmd_data['command_name']}\n"
                    content += f"     {cmd_data['content']}\n"
                    content += f"     数据格式: {cmd_data['data_type']}\n\n"

            # 添加合路器文件信息
            combiner_files = session_data.get("combiner_files", {})
            if combiner_files:
                content += "合路器信息文件:\n"
                content += "-" * 50 + "\n"
                for file_type, file_path in combiner_files.items():
                    content += f"  {file_type}: {file_path}\n"
                content += "\n"

            content += f"文件生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"测试会话数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存测试会话数据失败: {e}")
            return None

    def _save_individual_command_data(self, command_name: str, response_data: Dict, **kwargs) -> Optional[str]:
        """
        单独保存指令数据（回退方案）

        Args:
            command_name: 指令名称
            response_data: 响应数据
            **kwargs: 额外参数

        Returns:
            str: 保存的文件路径
        """
        try:
            if command_name == "回读RCU序列号":
                return self._save_rcu_serial_data(response_data)
            elif command_name == "回读备份序列号":
                return self._save_backup_serial_data(response_data)
            elif command_name == "回读电子标签":
                return self._save_electronic_tag_data(response_data)
            elif command_name == "查询软硬件版本信息":
                return self._save_version_info_data(response_data)
            elif command_name == "读取电机转速":
                return self._save_motor_speed_data(response_data)
            elif command_name in ["读取电压值", "读取电流值"]:
                return self._save_power_data(command_name, response_data)
            else:
                return None

        except Exception as e:
            self._log_error(f"单独保存指令数据失败 - {command_name}: {e}")
            return None
    
    def _save_rcu_serial_data(self, response_data: Dict) -> Optional[str]:
        """
        保存RCU序列号数据
        
        Args:
            response_data: 响应数据
            
        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "serial_number" not in response_data:
                return None
            
            serial_number = response_data["serial_number"]
            
            # 生成文件名
            filename = self._generate_timestamp_filename("回读RCU序列号")
            filepath = os.path.join(self.txt_save_dir, filename)
            
            # 保存到txt文件
            content = f"RCU序列号: {serial_number}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self._log_info(f"RCU序列号数据已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self._log_error(f"保存RCU序列号数据失败: {e}")
            return None
    
    def _save_backup_serial_data(self, response_data: Dict) -> Optional[str]:
        """
        保存备份序列号数据
        
        Args:
            response_data: 响应数据
            
        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "backup_serial_number" not in response_data:
                return None
            
            backup_serial_number = response_data["backup_serial_number"]
            
            # 生成文件名
            filename = self._generate_timestamp_filename("回读备份序列号")
            filepath = os.path.join(self.txt_save_dir, filename)
            
            # 保存到txt文件
            content = f"备份序列号: {backup_serial_number}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self._log_info(f"备份序列号数据已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self._log_error(f"保存备份序列号数据失败: {e}")
            return None

    def _save_electronic_tag_data(self, response_data: Dict) -> Optional[str]:
        """
        保存电子标签数据

        Args:
            response_data: 响应数据

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid"):
                return None

            frame_num = response_data.get("frame_num", 0)

            # 生成文件名
            filename = self._generate_timestamp_filename(f"回读电子标签_帧{frame_num}")
            filepath = os.path.join(self.txt_save_dir, filename)

            # 保存到txt文件
            content = f"电子标签数据(帧{frame_num}):\n"

            if "tag_content" in response_data:
                content += f"标签内容(ASCII): {response_data['tag_content']}\n"

            if "tag_data" in response_data:
                tag_data = response_data["tag_data"]
                content += f"原始数据(十六进制): {tag_data.hex().upper()}\n"
                # 尝试ASCII解码
                try:
                    ascii_content = tag_data.decode('ascii', errors='ignore')
                    if ascii_content.strip():
                        content += f"ASCII解码: {ascii_content}\n"
                except:
                    pass

            if "data_length" in response_data:
                content += f"数据长度: {response_data['data_length']} 字节\n"

            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"电子标签数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存电子标签数据失败: {e}")
            return None

    def _save_version_info_data(self, response_data: Dict) -> Optional[str]:
        """
        保存软硬件版本信息数据

        Args:
            response_data: 响应数据

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid"):
                return None

            # 生成文件名
            filename = self._generate_timestamp_filename("查询软硬件版本信息")
            filepath = os.path.join(self.txt_save_dir, filename)

            # 保存到txt文件
            content = f"软硬件版本信息:\n"

            # 添加所有可用的版本信息
            for key, value in response_data.items():
                if key != "valid" and value is not None:
                    content += f"{key}: {value}\n"

            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"软硬件版本信息数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存软硬件版本信息数据失败: {e}")
            return None

    def _save_motor_speed_data(self, response_data: Dict) -> Optional[str]:
        """
        保存电机转速数据

        Args:
            response_data: 响应数据

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "motor_speed" not in response_data:
                return None

            motor_speed = response_data["motor_speed"]

            # 生成文件名
            filename = self._generate_timestamp_filename("读取电机转速")
            filepath = os.path.join(self.txt_save_dir, filename)

            # 保存到txt文件
            content = f"电机转速: {motor_speed}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"电机转速数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存电机转速数据失败: {e}")
            return None

    def _save_power_data(self, command_name: str, response_data: Dict) -> Optional[str]:
        """
        保存电源数据（电压值或电流值）

        Args:
            command_name: 指令名称
            response_data: 响应数据

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("success") or not response_data.get("parsed_data"):
                return None

            parsed_data = response_data["parsed_data"]

            # 生成文件名
            filename = self._generate_timestamp_filename(command_name)
            filepath = os.path.join(self.txt_save_dir, filename)

            # 保存到txt文件
            content = f"{command_name}数据:\n"

            if command_name == "读取电压值" and "voltage" in parsed_data:
                content += f"电压值: {parsed_data['voltage']}V\n"
            elif command_name == "读取电流值" and "current" in parsed_data:
                content += f"电流值: {parsed_data['current']}A\n"

            # 添加原始响应
            if "raw_response" in response_data:
                content += f"原始响应: {response_data['raw_response']}\n"

            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self._log_info(f"{command_name}数据已保存: {filepath}")
            return filepath

        except Exception as e:
            self._log_error(f"保存{command_name}数据失败: {e}")
            return None

    def _save_combiner_info_data(self, response_data: Dict, **kwargs) -> Optional[str]:
        """
        保存合路器信息文件数据

        Args:
            response_data: 响应数据
            **kwargs: 额外参数，包括file_type, frame_num等

        Returns:
            str: 保存的文件路径
        """
        try:
            if not response_data.get("valid") or "file_data" not in response_data:
                return None

            file_type = response_data.get("file_type", "未知类型")
            frame_num = response_data.get("frame_num", 0)
            file_data = response_data["file_data"]

            # 获取缓存键
            cache_key = f"{file_type}_{kwargs.get('session_id', 'default')}"

            # 初始化缓存
            if cache_key not in self.combiner_data_cache:
                self.combiner_data_cache[cache_key] = {
                    "file_type": file_type,
                    "frames": {},
                    "total_data": bytearray(),
                    "start_time": datetime.now()
                }

            # 添加当前帧数据到缓存
            cache = self.combiner_data_cache[cache_key]
            cache["frames"][frame_num] = file_data
            cache["total_data"].extend(file_data)

            self._log_debug(f"合路器信息文件帧{frame_num}数据已缓存，数据长度: {len(file_data)}")

            # 检查是否是最后一帧（只有明确指示才认为是最后一帧）
            is_last_frame = kwargs.get("is_last_frame", False)

            if is_last_frame:
                # 保存完整的二进制文件和txt文件
                return self._finalize_combiner_info_save(cache_key)
            else:
                # 中间帧，只记录日志
                self._log_info(f"合路器信息文件帧{frame_num}数据已接收，等待更多帧...")
                # 返回一个临时状态信息，但不是最终文件路径
                return f"frame_{frame_num}_cached"

        except Exception as e:
            self._log_error(f"保存合路器信息文件数据失败: {e}")
            return None

    def _finalize_combiner_info_save(self, cache_key: str) -> Optional[str]:
        """
        完成合路器信息文件的保存

        Args:
            cache_key: 缓存键

        Returns:
            str: txt文件路径
        """
        try:
            if cache_key not in self.combiner_data_cache:
                return None

            cache = self.combiner_data_cache[cache_key]
            file_type = cache["file_type"]
            total_data = bytes(cache["total_data"])
            frames = cache["frames"]

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_file_type = file_type.replace(" ", "_").replace("/", "_")

            # 保存二进制文件
            bin_filename = f"合路器信息文件_{safe_file_type}_{timestamp}.bin"
            bin_filepath = os.path.join(self.bin_save_dir, bin_filename)

            with open(bin_filepath, 'wb') as f:
                f.write(total_data)

            # 保存txt文件（包含二进制文件路径）
            txt_filename = f"合路器信息文件_{safe_file_type}_{timestamp}.txt"
            txt_filepath = os.path.join(self.txt_save_dir, txt_filename)

            content = f"合路器信息文件数据保存记录\n"
            content += f"文件类型: {file_type}\n"
            content += f"总帧数: {len(frames)}\n"
            content += f"总数据长度: {len(total_data)} 字节\n"
            content += f"二进制文件路径: {bin_filepath}\n"
            content += f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            # 添加各帧信息
            content += "各帧详细信息:\n"
            for frame_num in sorted(frames.keys()):
                frame_data = frames[frame_num]
                content += f"帧{frame_num}: {len(frame_data)} 字节\n"
                content += f"  数据(十六进制): {frame_data.hex().upper()}\n"

            with open(txt_filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            # 清理缓存
            del self.combiner_data_cache[cache_key]

            self._log_info(f"合路器信息文件数据保存完成:")
            self._log_info(f"  二进制文件: {bin_filepath}")
            self._log_info(f"  文本文件: {txt_filepath}")

            return txt_filepath

        except Exception as e:
            self._log_error(f"完成合路器信息文件保存失败: {e}")
            return None

    def clear_combiner_cache(self, file_type: str = None, session_id: str = None):
        """
        清理合路器信息文件缓存

        Args:
            file_type: 文件类型，None表示清理所有
            session_id: 会话ID，None表示清理所有
        """
        try:
            if file_type is None and session_id is None:
                # 清理所有缓存
                self.combiner_data_cache.clear()
                self._log_info("已清理所有合路器信息文件缓存")
            else:
                # 清理特定缓存
                keys_to_remove = []
                for key in self.combiner_data_cache.keys():
                    if file_type and not key.startswith(file_type):
                        continue
                    if session_id and not key.endswith(session_id):
                        continue
                    keys_to_remove.append(key)

                for key in keys_to_remove:
                    del self.combiner_data_cache[key]

                self._log_info(f"已清理合路器信息文件缓存: {keys_to_remove}")

        except Exception as e:
            self._log_error(f"清理合路器信息文件缓存失败: {e}")
