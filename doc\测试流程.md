# 测试流程
1. 点击开始测试按钮，弹出用户输入界面，用户输入21位条形码与内部序列号后点击确认开始进行测试。
2. 读取表格中第一行内容，根据COM口列内容连接对应串口。例如COM口内容为AISGIN，则在硬件配置页面找到AISGIN对应的串口名称。
3. 读取表格中第一行内容，根据名称列内容判断指令类型，生成需要发送的指令。
4. 将指令通过转义函数，函数功能是将除帧头帧尾外其它的7E转化为7D 5E，之后将指令通过串口发送。
5. 接收回传数据，根据发送指令的类型判断回传数据是否正确。若正确，则读取下一行内容。若错误，则终止测试流程，并将串口关闭。
6. 读取下一行内容时，查看该行串口与上一行是否一致。若一致则跳过连接串口的步骤，若不一致，则先关闭串口，再打开该行对应串口。读取指令，发送指令，校验回传指令步骤与第一行一致。校验正确后读取下一行内容。
7. 重复以上操作直到表格中数据被全部读取完毕，此时测试结束，关闭串口，用户重新输入21位条形码判断测试的是否为同一台设备。如果二次输入一致，则测试完成，否则测试失败。
# 测试中特定情况
1. 除去广播进入装备模式，广播恢复出厂序列号，响应帧，中间帧这四个指令，其余指令在发送完一帧并校验争取后，都要发送一帧响应帧。
2. 部分指令需要发送多帧指令，每帧都需要对回读数据进行校验，且每帧校验完成后都需要发送响应帧，在所有帧都发送完毕后再读取下一条指令。
3. 在处理回传指令时，也要先通过一个函数，将指令中7D 5E 转换为7E，再进行CRC校验。CRC校验后，固定接收指令直接与预设值进行判断是否正确；部分可变接收指令，根据关键字是否正确。
4. 除去固定指令与部分可变指令，还有一些指令需要发送多帧数据，校验时除了要校验每帧回传是否正确，还要校验接收完毕之后多帧数据的数据位整合校验是否争取。合路器信息文件要将所有接收到的帧的数据位整合在一起，与文件路径列中指向的文件作比较，判断是否一致；设备信息则根据内容列内容，确定校验全部信息还是校验软硬件版本信息，回传指令取出有效数据位转ASCII码与规格列内容作比较。
4. 在发送对应行指令时，该行背景颜色设置为黄色，如果该行指令校验通过，则设置为绿色，否则设置为红色。每次重新测试前恢复默认颜色。
5. 部分指令发送后下位机会回复7E 01 31 95 36 7E，此时不进行校验，而是发送7E 01 11 97 17 7E直到其不回复7E 01 31 95 36 7E为止。然后再对回复的数据进行校验。
6. 合路器信息文件要根据文件类型列内容确定时模块信息文件还是天线信息文件，根据文件路径列读取二进制文件。
7. 响应帧与中间帧没有内容信息，因此不会参与多帧数据整合校验步骤中。
