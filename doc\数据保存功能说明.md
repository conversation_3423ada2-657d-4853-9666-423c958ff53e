# 数据保存功能说明

## 概述

本功能为上位机自动化测试系统添加了数据保存能力，当执行特定指令时，会自动将回传指令的有效数据部分保存到文件中。

**重要特性**：
- **测试级别保存**：一次测试中的所有指令数据保存到同一个文件中
- **ASCII码格式**：除合路器信息文件外，其他指令数据都以ASCII码形式保存
- **默认启用**：数据保存功能默认开启，无需手动配置

## 支持的指令

系统支持以下指令的数据保存：

1. **回读合路器信息文件** - 特殊处理，保存二进制文件和txt文件（单独保存）
2. **回读RCU序列号** - ASCII码格式保存到测试文件
3. **回读备份序列号** - ASCII码格式保存到测试文件
4. **回读电子标签** - ASCII码格式保存到测试文件
5. **查询软硬件版本信息** - ASCII码格式保存到测试文件
6. **读取电机转速** - ASCII码格式保存到测试文件
7. **读取电压值** - ASCII码格式保存到测试文件
8. **读取电流值** - ASCII码格式保存到测试文件

## 保存目录结构

```
saved_data/
├── txt_files/          # 文本文件保存目录
│   ├── 测试数据_test_TEST123456_20250717_134620.txt    # 测试级别数据文件
│   ├── 合路器信息文件_模块信息文件_20250717_134620.txt  # 合路器文件（单独保存）
│   └── 回读RCU序列号_20250717_134620.txt              # 回退保存的单独文件
└── binary_files/       # 二进制文件保存目录
    └── 合路器信息文件_模块信息文件_20250717_134620.bin  # 合路器二进制文件
```

## 文件命名规则

### 测试级别数据文件
- **格式**: `测试数据_{会话ID}_{时间戳}.txt`
- **示例**: `测试数据_test_TEST123456_20250717_134620.txt`

### 合路器信息文件
- **格式**: `合路器信息文件_{文件类型}_{时间戳}.{扩展名}`
- **示例**: `合路器信息文件_模块信息文件_20250717_134620.txt`

### 回退保存文件
- **格式**: `{指令名称}_{时间戳}.txt`
- **示例**: `回读RCU序列号_20250717_134620.txt`

**时间戳格式**: `YYYYMMDD_HHMMSS`

## 保存内容格式

### 测试级别数据文件内容
```
测试数据保存记录
==================================================
会话ID: test_TEST123456_20250717_134620
开始时间: 2025-07-17 13:46:20
结束时间: 2025-07-17 13:46:20

测试信息:
  barcode: TEST123456
  internal_sn: HW12345678901234567
  mode: 自动

指令数据记录 (共7条):
--------------------------------------------------
[01] 2025-07-17 13:46:20 - 回读RCU序列号
     RCU序列号: HW12345678901234567
     数据格式: ASCII

[02] 2025-07-17 13:46:20 - 回读备份序列号
     备份序列号: BK98765432109876543
     数据格式: ASCII

[03] 2025-07-17 13:46:20 - 回读电子标签
     电子标签(帧0): RFID_TAG_001
     数据格式: ASCII

[04] 2025-07-17 13:46:20 - 查询软硬件版本信息
     软硬件版本信息: V1.0.0_20231201
     数据格式: ASCII

[05] 2025-07-17 13:46:20 - 读取电机转速
     电机转速: 128
     数据格式: ASCII

[06] 2025-07-17 13:46:20 - 读取电压值
     电压值: 12.5V
     数据格式: ASCII

[07] 2025-07-17 13:46:20 - 读取电流值
     电流值: 2.35A
     数据格式: ASCII

文件生成时间: 2025-07-17 13:46:20
```

### 回读合路器信息文件（特殊处理）

**txt文件内容**:
```
合路器信息文件数据保存记录
文件类型: 模块信息文件
总帧数: 3
总数据长度: 109 字节
二进制文件路径: saved_data\binary_files\合路器信息文件_模块信息文件_20250717_115023.bin
保存时间: 2025-07-17 11:50:23

各帧详细信息:
帧0: 66 字节
  数据(十六进制): 48656C6C6F20576F726C64...
帧1: 40 字节
  数据(十六进制): 54657374204461746121...
帧2: 3 字节
  数据(十六进制): 454E44
```

**二进制文件**: 包含所有帧的原始数据，按帧顺序拼接

## 技术实现

### 核心组件

1. **DataSaver类** (`function/data_saver.py`)
   - 负责数据保存逻辑
   - 管理合路器信息文件的多帧缓存
   - 生成文件名和保存路径

2. **CommandHandler集成**
   - 在`parse_response_data`方法中自动调用数据保存
   - 支持普通指令和电源指令

3. **TestController集成**
   - 在指令执行成功后显示保存信息
   - 处理电源指令的特殊响应

### 合路器信息文件特殊处理

由于合路器信息文件可能包含多帧数据，系统采用以下策略：

1. **缓存机制**: 使用`file_type + session_id`作为缓存键
2. **帧数据累积**: 每帧数据都添加到缓存中
3. **最终保存**: 只有在明确标记`is_last_frame=True`时才保存文件
4. **数据整合**: 将所有帧数据拼接成完整的二进制文件

## 使用方式

### 自动保存
系统会在指令执行成功后自动保存数据，无需手动操作。

### 手动测试
可以运行测试脚本验证功能：
```bash
python test_data_saver.py
```

### 日志信息
保存成功时会在日志中显示：
```
指令 回读RCU序列号 的响应数据已保存: saved_data\txt_files\回读RCU序列号_20250717_115023.txt
```

## 错误处理

1. **目录创建**: 自动创建保存目录
2. **文件写入失败**: 记录错误日志，不影响指令执行
3. **数据解析失败**: 跳过保存，记录错误信息
4. **不支持的指令**: 自动跳过，不进行保存

## 注意事项

1. **存储空间**: 长期运行可能产生大量文件，建议定期清理
2. **文件权限**: 确保程序对保存目录有写入权限
3. **合路器文件**: 多帧数据需要正确标记最后一帧
4. **时间戳**: 文件名使用本地时间，注意时区设置

## 扩展支持

如需添加新指令的数据保存支持：

1. 在`DataSaver.supported_commands`中添加指令名称
2. 在`save_command_response_data`方法中添加处理逻辑
3. 实现对应的`_save_xxx_data`方法
4. 更新本文档

## 测试验证

系统已通过以下测试：
- ✓ 所有支持指令的数据保存
- ✓ 合路器信息文件多帧处理
- ✓ 文件内容完整性验证
- ✓ 错误处理和异常情况
- ✓ 不支持指令的正确拒绝
