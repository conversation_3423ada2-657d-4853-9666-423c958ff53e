# 指令类型
## 发送指令
### 固定指令
1. 广播进入装备模式：7E FF BB 90 06 00 48 57 03 01 01 01 9D 29 7E
2. 广播恢复出厂序列号：7E FF BB 90 05 00 48 57 03 02 00 A1 83 7E
3. 默认序列号分配地址：7E FF BF 81 F0 18 01 13 48 57 5F 4E 45 45 44 53 5F 41 4E 54 5F 43 4F 4E 46 49 47 02 01 01 F7 C7 7E
4. 写温度信息：7E 01 10 90 05 00 48 57 88 00 19 A9 8E 7E
5. 回读温度信息：7E 01 10 90 03 00 48 57 89 59 AA 7E
6. Flash MINI自检：7E 01 10 90 04 00 48 57 56 04 90 B9 7E
7. 查询软硬件版本信息：7E 01 10 0E 12 00 02 51 49 30 30 30 36 30 30 30 30 30 30 30 30 30 30 30 EE B2 7E 等回复后发送响应帧 再回复后发送 7E 01 10 05 00 00 2F 3E 7E
8. 设备1分配地址并建立连接：7E FF BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 31 02 01 01 91 A8 7E
9. 天线校准命令：7E 01 10 31 00 00 E0 DB 7E
10. 读取电机转速：7E 01 10 90 04 00 48 57 90 00 CE 61 7E
11. 设备2分配地址并建立连接：7E FF BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 32 02 01 01 5C 8D 7E
12. 备份序列号：7E 01 10 90 03 00 48 57 EA C4 FB 7E
13. 回读RCU序列号：7E 01 10 90 04 00 48 57 80 06 69 91 7E
14. 回读备份序列号：7E 01 10 90 03 00 48 57 EB 4D EA 7E
15. 响应帧：7E 01 93 8D B0 7E
16. 中间帧：7E 01 11 97 17 7E
### 部分可变指令
1. 回读电子标签：7E 01 10 90 04 00 48 57 11 帧编号（0x00-0x03） CRC16（低位，高位） 7E
2. 回读合路器信息文件：
    1. 模块信息文件：7E 01 10 90 05 00 48 57 87 05 帧编号 CRC16（低位，高位） 7E
    2. 天线信息文件：7E 01 10 90 05 00 48 57 87 06 帧编号 CRC16（低位，高位） 7E
.. 写RCU序列号：7E 01 10 90 15 00 48 57 81 06 （根据用户输入的内部序列号，其后17位的ASCII码） CRC16（低位，高位） 7E
4. 对设备分配地址并建链：7E FF BF 81 F0 18 01 13 48 57 （写入的RCU序列号） CRC16（低位，高位） 7E
5. 设备设置装备模式：7E 01 10 90 04 00 48 57 50 01/00（进入/退出） CRC16（低位，高位） 7E
6. 设置假负载开关：7E 01 10 90 06 00 48 57 27 21 01 01/00（开/关） CRC16（低位，高位） 7E
7. 设置天线倾角：7E 01 10 33 02 00 （两字节有符号数，低位在前） CRC16（低位，高位） 7E
### 特殊指令
1. 写合路器信息文件：根据FileType判断是模块文件还是天线文件，根据FilePath读取文件。信息文件为二进制文件，读取后按照0x48个字节为一组，发送写文件指令。
    1. 模块文件指令格式为：7E 01 10 90 信息域长度（48 00，低位在前） 48 57 86 05 帧编号 数据（长度为信息域长度 - 5 字节） CRC16（低位，高位） 7E。若是最后一帧信息域长度不足0x48字节，则改变信息域长度大小，按照实际数据组成指令。
    2. 天线文件指令格式为：7E 01 10 90 信息域长度（48 00，低位在前） 48 57 86 06 帧编号 数据（长度为信息域长度 - 5 字节） CRC16（低位，高位） 7E。若是最后一帧信息域长度不足0x48字节，则改变信息域长度大小，按照实际数据组成指令。
2. 写电子标签：7E 01 10 90 信息域长度（66 00，低位在前） 48 57 10 帧编号 数据（长度为信息域长度 - 4 字节） CRC16（低位，高位） 7E。若是最后一帧信息域长度不足0x66字节，则改变信息域长度大小，按照实际数据组成指令。
电子标签格式如下，将其转换为ASCII码，换行使用0D0A表示，括号内为备注。生成的电子标签在开头加上9A 86 76 01四个字节的十六进制数，结尾加上00十六进制数组成完整的电子标签。
/$[ArchivesInfo Version]
/$ArchivesInfoVersion = 3.0


[Board Properties]
BoardType=AIMM20D11v04
BarCode=21271514343AR5000006（根据用户输入的20位序列号）
Item=27151434（根据用户输入的20位序列号2-10位，共8位）
Description=Antenna Accessory,AIMM20D11v04,Antenna Information Management Module,Huawei Antenna,DC 10~30V,AISG2.0,a pair of AISG connectors,1in 1out
Manufactured=2025-06-07（当前日期的ASCII码）
VendorName=Huawei
IssueNumber=00
CLEICode=
BOM=
## 接收指令
### 固定指令
1. 广播进入装备模式回复：7E 00 BB 90 04 00 48 57 03 00 78 C5 7E
2. 广播恢复出厂序列号回复：7E 00 BB 90 04 00 48 57 03 00 78 C5 7E
3. 默认序列号分配地址回复：7E 01 BF 81 F0 18 01 13 48 57 5F 4E 45 45 44 53 5F 41 4E 54 5F 43 4F 4E 46 49 47 04 01 01 F5 02 7E
4. 设备1分配地址并建立连接回复：7E 01 BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 31 04 01 01 93 6D 7E
5. 设备2分配地址并建立连接回复：7E 01 BF 81 F0 18 01 13 48 57 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 32 04 01 01 5E 48 7E
7. 写温度信息回复：7E 01 30 90 04 00 48 57 88 00 6F 8C 7E 
8. 查询软硬件版本信息回复：7E 01 30 05 48 00 00 0C 41 49 4D 4D 32 30 44 31 31 76 30 33 11 5F 4E 45 45 44 53 5F 41 4E 54 5F 43 4F 4E 46 49 47 11 57 44 38 33 4D 41 4E 57 52 43 55 5F 56 45 52 2E 41 15 56 35 30 30 52 30 30 34 43 30 30 53 50 43 32 30 30 42 39 30 31 7C 90 7E
9. 天线校准命令回复：7E 01 30 31 01 00 00 E0 2D 7E 
10. 写RCU序列号回复：7E 01 30 90 04 00 48 57 81 00 77 5B 7E
11. 备份序列号回复：7E 01 30 90 04 00 48 57 EA 00 8A DA 7E
12. 响应帧回复：7E 01 73 83 57 7E
13. 中间帧前置：7E 01 31 95 36 7E，接收到该指令后发送 7E 01 11 97 17 7E
14. 设备设置装备模式回复：7E 01 30 90 04 00 48 57 50 00 94 1D 7E
### 部分可变指令
1. 回读温度信息回复：7E 01 30 90 06 00 48 57 89 00 （两字节有符号数，低位在前） CRC16（低位，高位） 7E
2. 读取电机转速回复：7E 01 30 90 05 00 48 57 90 00 1字节无符号数 CRC16（低位，高位） 7E
3. 写电子标签回复：7E 01 30 90 05 00 48 57 10 00 帧编号 CRC16（低位，高位） 7E
4. 回读RCU序列号回复：7E 01 30 90 16 00 48 57 80 00 06  （用户输入序列号后17位的ASCII码）  CRC16（低位，高位） 7E
5. 回读备份序列号回复：7E 01 30 90 17 00 48 57 EB 00 48 57 （用户输入序列号后17位的ASCII码） CRC16（低位，高位） 7E
6. 对设备分配地址并建链回复：7E 01 BF 81 F0 18 01 13 48 57  （用户输入序列号后17位的ASCII码）  04 01 01 CRC16（低位，高位） 7E
### 特殊指令
1. Flash MINI自检回复：
    1. 正常情况回复：7E 01 30 90 04 00 48 57 56 00 44 49 7E
    2. 特殊情况回复：7E 01 31 95 36 7E，后续发送7E 01 11 97 17 7E，直到接收到正常回复7E 01 30 90 04 00 48 57 56 00 44 49 7E
2. 写合路器信息文件：
    1. 模块文件指令回复：
        1. 正常情况：7E 01 30 90 06 00 48 57 86 00 05 帧编号 CRC16（低位，高位） 7E
        2. 特殊情况:最后一帧会先回复7E 01 31 95 36 7E，发送中间帧直到收到7E 01 30 90 06 00 48 57 86 00 05 26 1F 38 7E
    2. 天线信息指令回复：
        1. 正常情况：7E 01 30 90 06 00 48 57 86 00 06 帧编号 CRC16（低位，高位） 7E
        2. 特殊情况:最后一帧会先回复7E 01 31 95 36 7E，发送中间帧直到收到7E 01 30 90 06 00 48 57 86 00 06 0E 3D BF 7E
3. 读合路器信息回复:
    1. 模块文件指令回复：7E 01 30 90 信息域长度（48 00，低位在前） 48 57 87 00 05 帧编号 CRC16（低位，高位）数据（长度为信息域长度 - 6 字节） 7E 若是最后一帧信息域长度不足0x48字节，则改变信息域长度大小，按照实际数据组成指令。
    2. 天线信息指令回复：7E 01 30 90 信息域长度（48 00，低位在前） 48 57 87 00 05 帧编号 CRC16（低位，高位）数据（长度为信息域长度 - 6 字节） 7E 若是最后一帧信息域长度不足0x48字节，则改变信息域长度大小，按照实际数据组成指令。

20位：21271514343AR5000006
19位：HWN1434R5001C000000