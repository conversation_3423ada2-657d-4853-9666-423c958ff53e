# 上位机自动化测试开发详细说明

## 一、总体架构建议

1. **界面布局**  
    参考[Main_Window.py](Main_Window.py)文件界面布局。

2. **核心模块**  
   - **串口通信模块**：负责串口的打开、关闭、数据收发、异常处理。
   - **指令生成与解析模块**：根据表格内容和指令类型生成协议指令，并对回传数据进行解析和校验。
   - **流程控制模块**：自逐行执行测试步骤，处理流程跳转和异常。
   - **界面交互模块**：负责用户输入、表格数据显示、状态反馈、颜色高亮等。

---

## 二、测试流程实现要点

1. **用户输入与测试启动**
   - 用户点击“开始测试”后，弹窗输入条形码和内部序列号，校验格式后进入测试主流程。

2. **表格驱动测试**
   - 逐行读取测试表格，每行包含：序号、COM口、名称、内容、规格、文件类型、文件路径等。
   - 根据“COM口”列内容，自动匹配硬件配置区的串口名称，动态切换串口连接。
   - 根据“名称”列内容，自动匹配指令类型，调用指令生成模块生成协议指令。

3. **指令处理**
   - 指令生成时，除帧头帧尾外，其余7E需转义为7D 5E。
   - 发送指令后，等待回传数据，超时或异常需有错误提示。
   - 回传数据需根据指令类型进行解析和校验，部分指令需将回传内容转ASCII后与“规格”列比对。
   - 对于需要多帧发送的指令（如文件传输），每帧都需校验回读数据并发送响应帧，全部完成后进入下一条指令。
   - 特殊帧处理：如遇到下位机回复7E 01 31 95 36 7E，需循环发送7E 01 11 97 17 7E，直到收到正常回复。

4. **文件操作**
   - 根据“文件类型”与“文件路径”列，读取本地二进制文件，分帧发送，每帧需校验回传。
   - 合路器信息文件等需整合所有帧数据，与本地文件比对。

5. **流程控制与界面反馈**
   - 每发送一条指令，将当前行背景色设为黄色，校验通过设为绿色，失败设为红色。
   - 测试失败时终止流程并关闭串口，测试通过则继续下一行。
   - 支持自动与手动两种模式，手动模式下可逐步执行每一步，便于调试。

6. **测试结束**
   - 所有行测试完成后关闭串口。
   - 用户再次输入条形码，判断是否为同一台设备，决定测试是否通过。

---

## 三、指令类型与协议说明

1. **指令分类**  
   - 广播类指令：如进入装备模式、恢复出厂序列号等，通常不需回传校验。
   - 配置与信息写入类：如分配地址、写温度信息等，需回传校验。
   - 信息读取类：如回读温度信息，需解析回传内容并与规格比对。
   - 自检与状态检测类：如Flash MINI自检，需判断回传状态。
   - 文件传输类：如模块/天线信息文件，需分帧发送并校验。

2. **指令格式**  
   - 所有指令以7E为帧头和帧尾，内容需按协议转义。
   - 指令内容根据表格“名称”列和协议文档动态生成。
   - 回传数据需按协议解析，部分需转ASCII码比对。

3. **响应帧与特殊帧处理**  
   - 除部分广播和特殊指令外，绝大多数指令需在校验通过后发送响应帧。
   - 多帧指令需每帧都处理回传和响应。
   - 特殊帧需循环处理，直到收到正常回复。

---

## 四、开发建议

- **模块化设计**：将串口、指令、流程、界面等功能解耦，便于维护和扩展。
- **异常处理**：所有串口和文件操作需有异常捕获，保证资源及时释放。
- **日志记录**：关键操作和异常需写入日志，便于问题追踪。
- **可配置性**：指令类型、协议参数、串口映射等建议通过配置文件管理。
- **测试与调试**：提供手动模式和详细日志，便于开发和现场调试。

---

## 五、参考资料

- [《测试流程.md》](测试流程.md)：详细描述了测试步骤和流程控制逻辑。
- [《指令类型.md》](指令类型.md)：列举了所有指令类型及其功能和格式。
- 协议文档：请结合下位机通信协议，确保指令格式和校验方式一致。

---