# -*- coding: utf-8 -*-
"""
测试流程控制器 - 支持完整的多帧指令处理和优先级响应
使用状态机模式管理测试执行流程，支持暂停、恢复、停止等操作
"""

import sys
import json
import time
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem
from PyQt5.QtCore import Qt

class TestStatus(Enum):
    """测试状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    COMPLETED = "completed"
    FAILED = "failed"

class TestStepStatus(Enum):
    """测试步骤状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"

class TestController(QThread):
    """测试流程控制器，支持完整的多帧指令处理和优先级响应"""
    
    # 信号定义 - 保持与原始接口兼容
    test_started = pyqtSignal()  # 测试开始
    test_completed = pyqtSignal(bool, str)  # 测试完成 (是否成功, 消息)
    step_started = pyqtSignal(int, str)  # 步骤开始 (行号, 指令名)
    step_completed = pyqtSignal(int, bool, str)  # 步骤完成 (行号, 是否成功, 消息)
    status_changed = pyqtSignal(str)  # 状态改变
    progress_updated = pyqtSignal(int, str)  # 进度更新 (行号, 状态) - 保持原有格式
    ui_append_back_data = pyqtSignal(int, str)  # 更新接收数据显示
    ui_append_result_data = pyqtSignal(int, str, str, str)  # 更新结果数据 (行号, 指令名, 结果, 消息)
    
    def __init__(self, serial_manager, command_handler, config_manager, logger_manager, file_manager):
        """使用原始的构造函数参数顺序以保持兼容性"""
        super().__init__()
        self.serial_manager = serial_manager
        self.command_handler = command_handler
        self.config_manager = config_manager
        self.logger_manager = logger_manager
        self.file_manager = file_manager
        self.ui_manager = None
        
        # 测试状态 - 保持与原始接口兼容
        self.status = TestStatus.IDLE
        self.test_data = {}
        self.test_cases = []
        self.current_row = 0
        self.total_rows = 0
        self.barcode = ""
        self.internal_sn = ""
        self.is_running = False
        
        # 原始测试控制器的属性
        self.is_manual_mode = False
        self.should_stop = False
        self.should_pause = False
        self.table_widget = None
        self.status_callback = None
        self.passed_steps = 0
        self.failed_steps = 0
        self.error_messages = []
        
        # 串口连接状态缓存
        self.current_connected_port = None  # 当前连接的实际串口
        self.current_device_name = None     # 当前连接的设备名称
        
    def set_ui_components(self, table_widget, status_callback=None):
        """设置UI组件引用"""
        self.table_widget = table_widget
        self.status_callback = status_callback
        
    def set_ui_manager(self, ui_manager):
        """设置UI管理器引用"""
        self.ui_manager = ui_manager
        self.logger_manager.log_info(f"TestController UI管理器已设置: {ui_manager is not None}")
        
    def load_test_data(self, test_data: dict) -> bool:
        """加载测试数据"""
        try:
            self.test_data = test_data
            # 转换为测试用例列表
            self.test_cases = []
            for row_num, row_data in test_data.items():
                if isinstance(row_data, dict):
                    self.test_cases.append(row_data)
            
            self.total_rows = len(self.test_cases)
            self.current_row = 0
            self.passed_steps = 0
            self.failed_steps = 0
            self.error_messages = []
            
            self.logger_manager.log_info(f"加载测试数据完成，总计 {self.total_rows} 个测试步骤", "test")
            return True
            
        except Exception as e:
            self.logger_manager.log_error(f"加载测试数据失败", "error", e)
            return False
    
    def start_test(self, barcode: str, internal_sn: str, manual_mode: bool = False) -> bool:
        """开始测试"""
        try:
            if self.status != TestStatus.IDLE:
                self.logger_manager.log_warning("测试已在运行中", "test")
                return False
            
            # 设置测试参数
            self.barcode = barcode
            self.internal_sn = internal_sn
            self.is_manual_mode = manual_mode
            self.should_stop = False
            self.should_pause = False
            
            # 重置测试状态
            self.current_row = 0
            self.passed_steps = 0
            self.failed_steps = 0
            self.error_messages = []
            # 🔧 修复：重置已执行行集合，避免天线校准指令被跳过
            self._executed_rows = set()
            
            # 启动测试线程
            self._change_status(TestStatus.RUNNING)
            self.test_started.emit()
            self.start()
            
            self.logger_manager.log_info(f"开始测试 - 条形码: {barcode}, 序列号: {internal_sn}, 模式: {'手动' if manual_mode else '自动'}", "test")
            return True
            
        except Exception as e:
            self.logger_manager.log_error(f"启动测试失败", "error", e)
            self._change_status(TestStatus.FAILED)
            return False
    
    def stop_test(self):
        """停止测试"""
        self.should_stop = True
        self.is_running = False
        if self.status in [TestStatus.RUNNING, TestStatus.PAUSED]:
            self._change_status(TestStatus.STOPPED)
        self.logger_manager.log_info("测试流程已停止")
        
    def pause_test(self):
        """暂停测试"""
        if self.status == TestStatus.RUNNING:
            self.should_pause = True
            self._change_status(TestStatus.PAUSED)
            self.logger_manager.log_info("测试已暂停", "test")
    
    def resume_test(self):
        """恢复测试"""
        if self.status == TestStatus.PAUSED:
            self.should_pause = False
            self._change_status(TestStatus.RUNNING)
            self.logger_manager.log_info("测试已恢复", "test")
    
    def _change_status(self, new_status: TestStatus):
        """改变测试状态"""
        if self.status != new_status:
            old_status = self.status
            self.status = new_status
            self.status_changed.emit(new_status.value)
            self.logger_manager.log_debug(f"测试状态变更: {old_status.value} -> {new_status.value}")
    
    def _reset_table_colors(self):
        """重置表格颜色"""
        if self.table_widget:
            for row in range(self.table_widget.rowCount()):
                self._set_row_color(row, "white")
    
    def _set_row_color(self, row: int, color: str):
        """设置表格行颜色"""
        if self.table_widget and row < self.table_widget.rowCount():
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QColor
            
            color_map = {
                "white": QColor(255, 255, 255),
                "yellow": QColor(255, 255, 0),
                "green": QColor(0, 255, 0),
                "red": QColor(255, 0, 0),
                "gray": QColor(128, 128, 128)
            }
            
            bg_color = color_map.get(color, QColor(255, 255, 255))
            
            for col in range(self.table_widget.columnCount()):
                item = self.table_widget.item(row, col)
                if item:
                    item.setBackground(bg_color)
    
    def get_test_summary(self) -> dict:
        """获取测试摘要"""
        return {
            "total_steps": self.total_rows,
            "passed_steps": self.passed_steps,
            "failed_steps": self.failed_steps,
            "current_row": self.current_row,
            "status": self.status.value,
            "error_messages": self.error_messages.copy()
        }
    
    def run(self):
        """执行测试流程"""
        try:
            self.is_running = True
            self.logger_manager.log_info("开始执行测试流程")
            
            for i, test_case in enumerate(self.test_cases):
                if not self.is_running or self.should_stop:
                    break
                    
                # 检查暂停状态
                while self.should_pause and not self.should_stop:
                    time.sleep(0.1)
                    
                if self.should_stop:
                    break
                    
                self.current_row = i
                command_name = test_case.get("名称", "")
                com_port = test_case.get("COM口", "")

                # 检查是否已在天线校准时执行过
                if self._is_row_executed(i):
                    self.logger_manager.log_info(f"第 {i+1} 行 ({command_name}) 已在天线校准时执行，跳过")
                    continue
                
                # 连接串口
                if not self._connect_to_port(com_port):
                    error_msg = f"连接串口失败: {com_port}"
                    self.failed_steps += 1
                    self.error_messages.append(f"第{i+1}行: {error_msg}")
                    self.step_completed.emit(i, False, error_msg)
                    self.logger_manager.log_error(f"测试用例 {i+1} 执行失败: {error_msg}")
                    
                    # 测试失败，结束测试
                    self._change_status(TestStatus.FAILED)
                    self.test_completed.emit(False, f"测试在第 {i+1} 行失败: {error_msg}")
                    return
                
                # 发出步骤开始信号
                self.step_started.emit(i, command_name)
                self.progress_updated.emit(i, "执行中")
                
                # 设置行颜色为黄色（执行中）
                self._set_row_color(i, "yellow")
                
                success, message = self._execute_command(test_case)
                
                if success:
                    self.passed_steps += 1
                    self.progress_updated.emit(i, "成功")
                    self.step_completed.emit(i, True, message)
                    # 设置行颜色为绿色（成功）
                    self._set_row_color(i, "green")
                    # 发出UI结果数据更新信号
                    self.ui_append_result_data.emit(i, command_name, "通过", message)
                    self.logger_manager.log_info(f"测试用例 {i+1} 执行成功: {message}")
                else:
                    self.failed_steps += 1
                    self.error_messages.append(f"第{i+1}行: {message}")
                    self.progress_updated.emit(i, "失败")
                    self.step_completed.emit(i, False, message)
                    # 设置行颜色为红色（失败）
                    self._set_row_color(i, "red")
                    # 发出UI结果数据更新信号
                    self.ui_append_result_data.emit(i, command_name, "失败", message)
                    self.logger_manager.log_error(f"测试用例 {i+1} 执行失败: {message}")
                    
                    # 测试失败，结束测试
                    self._change_status(TestStatus.FAILED)
                    self.test_completed.emit(False, f"测试在第 {i+1} 行失败: {message}")
                    return
                    
            # 所有测试用例执行完成
            if self.failed_steps == 0:
                self._change_status(TestStatus.COMPLETED)
                self.test_completed.emit(True, f"所有测试用例执行成功，共 {self.passed_steps} 个")
            else:
                self._change_status(TestStatus.FAILED)
                self.test_completed.emit(False, f"测试完成，{self.passed_steps} 个成功，{self.failed_steps} 个失败")
            
        except Exception as e:
            self.logger_manager.log_error("测试流程异常", "error", e)
            self.error_messages.append(f"测试流程异常: {str(e)}")
            self._change_status(TestStatus.FAILED)
            self.test_completed.emit(False, f"测试流程异常: {str(e)}")
        finally:
            self.is_running = False
            # 关闭串口
            self.serial_manager.close_port()
            
    def _connect_to_port(self, com_port: str) -> bool:
        """
        连接到指定串口
        优化：只在需要切换到不同串口时才重新连接
        
        Args:
            com_port: 串口名称（设备名称）
            
        Returns:
            bool: 是否连接成功
        """
        try:
            # 根据设备名称获取实际串口
            actual_port = self.config_manager.get_port_by_name(com_port)
            if not actual_port:
                self.logger_manager.log_warning(f"未找到设备 {com_port} 对应的串口")
                return False
            
            # 检查是否已经连接到目标串口
            if (self.current_connected_port == actual_port and 
                self.current_device_name == com_port and 
                self.serial_manager.ser and 
                self.serial_manager.ser.is_open):
                
                self.logger_manager.log_info(f"串口 {actual_port} 已连接，无需重复连接", "serial")
                return True
            
            # 需要切换串口，先关闭当前串口
            if self.current_connected_port:
                self.logger_manager.log_info(f"切换串口：从 {self.current_connected_port} 到 {actual_port}", "serial")
                self.serial_manager.close_port()
                self.current_connected_port = None
                self.current_device_name = None
            
            # 打开新串口
            if self.serial_manager.open_port(actual_port):
                self.logger_manager.log_info(f"成功连接到串口: {actual_port}", "serial")
                self.current_connected_port = actual_port
                self.current_device_name = com_port
                return True
            else:
                self.logger_manager.log_error(f"连接串口失败: {actual_port}", "serial")
                return False
                
        except Exception as e:
            self.logger_manager.log_error(f"连接串口异常: {com_port}", "error", e)
            return False
    
    def _execute_command(self, test_case: dict) -> Tuple[bool, str]:
        """
        使用增强逻辑执行指令
        
        Args:
            test_case: 测试用例字典
            
        Returns:
            tuple: (是否成功, 消息)
        """
        try:
            command_name = test_case.get("名称", "")
            content = test_case.get("内容", "")
            spec = test_case.get("规格", "")
            file_type = test_case.get("文件类型", "")
            file_path = test_case.get("文件路径", "")
            
            self.logger_manager.log_info(f"执行指令: {command_name}")
            
            # 特殊处理：异步天线校准序列
            if command_name == "天线校准命令":
                return self._execute_async_antenna_calibration()
            
            # 检查是否是多帧指令
            if self.command_handler.is_multi_frame_command(command_name):
                return self._execute_multi_frame_command(
                    command_name, content, spec, file_type, file_path
                )
            else:
                return self._execute_single_frame_command(
                    command_name, content, spec, file_type, file_path
                )
                
        except Exception as e:
            return False, f"执行指令异常: {str(e)}"
            
    def _execute_multi_frame_command(self, command_name: str, content: str, 
                                   spec: str, file_type: str, file_path: str) -> Tuple[bool, str]:
        """执行多帧指令"""
        try:
            # 准备参数
            params = {
                'content': content,
                'file_path': file_path,
                'file_type': file_type  # 添加 file_type 参数
            }
            
            # 开始指令序列
            self.command_handler.start_command_sequence(command_name, **params)
            
            state = self.command_handler.get_command_state_info()
            total_frames = state["total_frames"]
            
            self.logger_manager.log_info(f"开始多帧指令: {command_name}, 总帧数: {total_frames}")
            
            frame_count = 0
            while frame_count < total_frames:
                # 生成当前帧指令
                frame_command = self.command_handler.get_next_frame_command()
                if frame_command is None:
                    return False, f"生成第 {frame_count} 帧指令失败"
                
                # 转义并发送指令
                escaped_command = self.command_handler.escape_command(frame_command)
                
                self.logger_manager.log_info(f"发送第 {frame_count} 帧")
                if not self.serial_manager.send_command(
                    escaped_command, f"FRAME_{frame_count}"
                ):
                    return False, f"发送第 {frame_count} 帧失败"
                
                # 处理响应和后续动作
                success, message = self._handle_frame_response_sequence(frame_count)
                if not success:
                    return False, message
                    
                # 检查是否需要继续下一帧
                state = self.command_handler.get_command_state_info()
                
                # 强制帧编号按顺序递增，不管command_handler中的current_frame是什么
                frame_count += 1
                
                # 如果内部帧编号与我们的不一致，记录警告但继续使用顺序递增的帧编号
                if state["current_frame"] != frame_count:
                    # self.logger_manager.log_warning(f"内部帧编号({state['current_frame']})与顺序递增帧编号({frame_count})不一致，使用顺序递增帧编号")
                    # 更新内部帧编号，确保一致性
                    self.command_handler.command_state["current_frame"] = frame_count
                
                # 如果是天线信息文件的最后一帧，等待1秒
                if frame_count == total_frames and command_name in ["写天线文件", "写合路器信息文件"] and file_type == "天线信息文件":
                    self.logger_manager.log_info("天线信息文件最后一帧发送完毕，等待1秒...")
                    time.sleep(1.0)
                    
            return True, f"多帧指令执行成功，共 {total_frames} 帧"
            
        except Exception as e:
            return False, f"多帧指令执行异常: {str(e)}"
            
    def _execute_single_frame_command(self, command_name: str, content: str, 
                                    spec: str, file_type: str, file_path: str) -> Tuple[bool, str]:
        """执行单帧指令"""
        try:
            # 准备参数
            params = self._prepare_command_params(command_name, content, spec,file_path, file_type)
            
            # 添加调试日志
            if command_name == "读取电机转速":
                self.logger_manager.log_info(f"读取电机转速指令参数 - content: '{content}', params: {params}")
            
            # 检查是否是电源指令
            power_commands = self.command_handler.power_handler.get_power_command_list()
            if command_name in power_commands:
                # 检查是否在校准期间且是电压/电流读取指令
                if (self.command_handler.power_handler.should_use_calibration_reading(command_name)):
                    self.logger_manager.log_info(f"校准期间执行特殊读取: {command_name}")

                    # 获取端口参数
                    port = params.get('port', '1')

                    # 执行校准期间的特殊读取逻辑
                    # 检查是否有motor_serial_manager（可选）
                    motor_serial_manager = getattr(self, 'motor_serial_manager', None)
                    calibration_result = self.command_handler.power_handler.execute_calibration_reading(
                        command_name, port, self.serial_manager, motor_serial_manager
                    )

                    if calibration_result["success"]:
                        # 处理校准读取结果显示
                        result_message = self._format_calibration_result(command_name, calibration_result)
                        self.logger_manager.log_info(result_message)

                        # 显示到UI
                        display_data = f"{result_message}\n详细数据: {calibration_result}".encode('utf-8')
                        self._display_received_data(display_data, f"校准{command_name}结果")

                        return True, result_message
                    else:
                        error_msg = calibration_result.get("message", "校准读取失败")
                        self.logger_manager.log_error(f"校准期间{command_name}失败: {error_msg}")
                        return False, f"校准期间{command_name}失败: {error_msg}"

                # 非校准期间或非电压/电流读取指令，使用普通电源指令处理
                self.logger_manager.log_info(f"执行电源指令: {command_name}")

                # 生成电源指令
                command_bytes = self.command_handler.generate_command(command_name, **params)
                if command_bytes is None:
                    return False, f"无法生成电源指令: {command_name}"

                # 发送电源指令
                if not self.serial_manager.send_power_command(command_bytes, command_name):
                    return False, "发送电源指令失败"

                # 尝试接收响应，但设置较短超时，不强制要求响应
                timeout = 0.1 if (command_name == "读取电流值" or command_name == "读取电压值") else 1.0
                response = self.serial_manager.receive_power_response(timeout)

                # 显示回传信息（如果有的话）
                if response:
                    response_str = response.decode('ascii', errors='ignore').strip()
                    self.logger_manager.log_info(f"电源指令响应: {response_str}")
                    # 显示到UI
                    self._display_received_data(response, f"电源指令响应")
                else:
                    self.logger_manager.log_debug(f"电源指令 {command_name} 无响应（正常情况）")

                # 电源指令不需要严格验证响应，有响应就显示，没响应也算成功
                self.logger_manager.log_info(f"电源指令 {command_name} 执行完成")
                return True, f"电源指令 {command_name} 执行成功"

            # 非电源指令使用原有逻辑
            # 生成指令
            command_bytes = self.command_handler.generate_command(command_name, **params)
            if command_bytes is None:
                return False, f"无法生成指令: {command_name}"

            # 添加调试日志
            if command_name == "读取电机转速":
                self.logger_manager.log_info(f"生成的指令: {command_bytes.hex().upper()}")

            # 转义并发送指令
            escaped_command = self.command_handler.escape_command(command_bytes)

            # 开始指令序列
            self.command_handler.start_command_sequence(command_name, **params)

            if not self.serial_manager.send_command(escaped_command, command_name):
                return False, "发送指令失败"

            # 处理特殊帧和接收响应
            success, frames = self.serial_manager.handle_special_frames_enhanced()
            if not success:
                return False, "处理特殊帧失败"
            
            # 如果特殊帧处理中已经收到了响应，使用该响应
            if frames:
                response = frames[-1]  # 使用最后一个帧作为响应
            else:
                # 否则接收新的响应
                response = self.serial_manager.receive_single_frame()
                if response is None:
                    return False, "接收响应超时"
            
            # 显示接收到的数据
            self._display_received_data(response)
            
            # 反转义响应
            unescaped_response = self.command_handler.unescape_response(response)
            
            # 验证响应
            if not self.command_handler.validate_response(command_name, unescaped_response, **params):
                # 验证失败时启用调试模式
                self.logger_manager.log_info(f"单帧指令 {command_name} 验证失败，启用调试模式")
                self.command_handler.validate_response(command_name, unescaped_response, debug_mode=True, **params)
                return False, "响应验证失败"
            
            # 解析响应数据并处理特定指令的数据
            self._process_data_and_display_results(command_name, unescaped_response)
            
            # 特殊处理：设置天线倾角指令的重发
            if command_name == "设置天线倾角" and hasattr(self.command_handler, '_needs_resend') and self.command_handler._needs_resend:
                # 发送响应帧
                response_frame = self.command_handler.get_response_frame()
                if not self.serial_manager.send_command(response_frame, "响应帧"):
                    return False, "发送响应帧失败"
                    
                # 等待响应帧回传
                echo_response = self.serial_manager.receive_single_frame(timeout=1.0)
                if echo_response != bytes.fromhex("7E 01 73 83 57 7E"):
                    self.logger_manager.log_error(f"响应帧回传验证失败，期望: 7E 01 73 83 57 7E, 实际: {echo_response.hex().upper() if echo_response else 'None'}")
                    return False, "响应帧回传验证失败"
                    
                self.logger_manager.log_info("收到天线校准完成响应，等待2秒后重发设置天线倾角指令")
                # 等待2秒再重发第二帧
                time.sleep(3)
                
                # 重发指令
                if not self.serial_manager.send_command(escaped_command, command_name):
                    return False, "重发设置天线倾角指令失败"
                
                # 处理第二次响应，包括可能的中间帧
                success, frames = self.serial_manager.handle_special_frames_enhanced()
                if not success:
                    return False, "处理第二次响应的特殊帧失败"
                
                # 如果特殊帧处理中已经收到了响应，使用该响应
                if frames:
                    response = frames[-1]  # 使用最后一个帧作为响应
                else:
                    # 否则接收新的响应
                    response = self.serial_manager.receive_single_frame()
                    if response is None:
                        return False, "接收第二次响应超时"
                
                # 显示第二次接收到的数据
                self._display_received_data(response, "重发响应")
                
                # 反转义第二次响应
                unescaped_response = self.command_handler.unescape_response(response)
                
                # 如果收到中间帧前置，需要发送中间帧
                if unescaped_response == bytes.fromhex("7E 01 31 95 36 7E"):
                    self.logger_manager.log_info("收到中间帧前置，发送中间帧")
                    middle_frame = self.command_handler.get_middle_frame()
                    if not self.serial_manager.send_command(middle_frame, "中间帧"):
                        return False, "发送中间帧失败"
                    
                    # 接收最终响应
                    response = self.serial_manager.receive_single_frame()
                    if response is None:
                        return False, "接收最终响应超时"
                    
                    # 显示最终响应数据
                    self._display_received_data(response, "最终响应")
                    
                    # 反转义最终响应
                    unescaped_response = self.command_handler.unescape_response(response)
                
                # 验证最终响应
                if not self.command_handler.validate_response(command_name, unescaped_response, **params):
                    self.logger_manager.log_info(f"设置天线倾角最终响应验证失败，启用调试模式")
                    self.command_handler.validate_response(command_name, unescaped_response, debug_mode=True, **params)
                    return False, "最终响应验证失败"
            
            # 发送响应帧（如果需要）
            if self.command_handler.needs_response_frame(command_name):
                response_frame = self.command_handler.get_response_frame()
                if not self.serial_manager.send_command(
                    response_frame, "RESPONSE_FRAME", is_response=True
                ):
                    return False, "发送响应帧失败"
                    
                # 等待响应帧回传
                echo_response = self.serial_manager.receive_single_frame(timeout=1.0)
                if echo_response != bytes.fromhex("7E 01 73 83 57 7E"):
                    self.logger_manager.log_error(f"响应帧回传验证失败，期望: 7E 01 73 83 57 7E, 实际: {echo_response.hex().upper() if echo_response else 'None'}")
                    return False, "响应帧回传验证失败"
                    
            return True, "单帧指令执行成功"
            
        except Exception as e:
            self.logger_manager.log_error(f"执行单帧指令失败: {str(e)}")
            return False, f"执行单帧指令失败: {str(e)}"
            
    def _handle_frame_response_sequence(self, frame_number: int) -> Tuple[bool, str]:
        """处理帧响应序列"""
        try:
            # 根据命令类型设置不同的迭代限制
            current_command = self.command_handler.get_command_state_info().get("current_command", "")
            
            if current_command == "天线校准命令":
                max_iterations = 5  # 天线校准只需要1次中间帧交互，设置较小的限制
                timeout = 3.0  # 天线校准超时时间
            elif current_command in ["写模块文件", "写天线文件", "写合路器信息文件"]:
                max_iterations = 200  # 文件传输可能需要较多中间帧
                timeout = 3.0
            else:
                max_iterations = 50  # 一般指令的默认限制
                timeout = 2.0  # 默认超时时间
            
            iteration = 0
            
            while iteration < max_iterations:
                iteration += 1
                self.logger_manager.log_debug(f"帧 {frame_number} 处理迭代 {iteration}/{max_iterations}")
                
                # 首先尝试接收响应
                response = self.serial_manager.receive_single_frame(timeout=timeout)
                if response is None:
                    if current_command == "天线校准命令" and iteration < max_iterations:
                        self.logger_manager.log_info(f"天线校准第 {iteration} 次超时，继续等待...")
                        continue
                    return False, f"接收第 {frame_number} 帧响应超时"
                
                # 记录日志但暂不显示到UI（等确认是有效响应再显示）
                self.logger_manager.log_info(f"帧 {frame_number} 收到响应: {response.hex().upper()}")
                
                # 反转义响应
                unescaped_response = self.command_handler.unescape_response(response)
                
                # 统一使用优先级处理逻辑，不再重复处理特殊帧
                result = self.command_handler.process_response_with_priority(unescaped_response)
                
                self.logger_manager.log_debug(f"帧 {frame_number} 处理结果: {result['message']}")
                
                # 如果验证失败，启用调试模式重新验证以获取详细信息
                if not result["validation_result"] and result["action"] == "validate":
                    self.logger_manager.log_info(f"帧 {frame_number} 验证失败，启用调试模式重新验证")
                    current_command = self.command_handler.get_command_state_info()["current_command"]
                    if current_command:
                        validate_params = self.command_handler.get_command_state_info()["command_params"].copy()
                        validate_params["frame_num"] = frame_number
                        # 启用调试模式重新验证
                        debug_result = self.command_handler.validate_response(
                            current_command, unescaped_response, debug_mode=True, **validate_params
                        )
                
                if result["action"] == "send_response_frame":
                    # 收到有效响应，显示到UI
                    self._display_received_data(response, f"帧{frame_number}")
                    
                    # 处理响应数据（如温度和电机转速）
                    current_command = self.command_handler.get_command_state_info()["current_command"]
                    self._process_data_and_display_results(current_command, unescaped_response)
                    
                    # 发送响应帧
                    response_frame = result["next_command"]
                    self.logger_manager.log_info(f"帧 {frame_number} 验证成功，发送响应帧")
                    
                    if not self.serial_manager.send_command(
                        response_frame, "RESPONSE_FRAME", is_response=True
                    ):
                        return False, "发送响应帧失败"
                    
                    # 等待响应帧回传，使用串口管理器的标准方法
                    self.logger_manager.log_debug(f"等待帧 {frame_number} 的响应帧回传")
                    
                    echo_response = self.serial_manager.receive_single_frame(timeout=3.0)
                    if echo_response is None:
                        self.logger_manager.log_error(f"帧 {frame_number} 响应帧回传超时")
                        return False, "响应帧回传超时"
                    
                    self.logger_manager.log_info(f"帧 {frame_number} 收到响应帧回传: {echo_response.hex().upper()}")
                    
                    # 验证回传是否正确
                    expected_echo = bytes.fromhex("7E 01 73 83 57 7E")
                    if echo_response == expected_echo:
                        self.logger_manager.log_info(f"帧 {frame_number} 响应帧回传验证成功")
                        
                        # 更新状态 - 手动调用优先级处理来更新帧状态
                        echo_result = self.command_handler.process_response_with_priority(echo_response)
                        
                        if echo_result["action"] == "continue_next_frame":
                            return True, f"第 {frame_number} 帧处理完成，准备下一帧"
                        elif echo_result["action"] == "command_complete":
                            return True, "指令完成"
                        else:
                            # 状态已更新，当前帧完成
                            return True, f"第 {frame_number} 帧处理完成"
                    else:
                        self.logger_manager.log_error(f"帧 {frame_number} 响应帧回传验证失败，期望: {expected_echo.hex().upper()}, 实际: {echo_response.hex().upper()}")
                        return False, "响应帧回传验证失败"
                    
                elif result["action"] == "send_middle_frame":
                    # 收到中间帧前置，不显示到UI，只记录日志
                    middle_frame = result["next_command"]
                    current_middle_count = self.command_handler.get_command_state_info().get("middle_frame_count", 0)
                    self.logger_manager.log_info(f"收到第 {current_middle_count} 个中间帧前置，发送中间帧响应")
                    if not self.serial_manager.send_command(
                        middle_frame, "MIDDLE_FRAME", is_response=True
                    ):
                        return False, "发送中间帧失败"
                    continue  # 继续处理
                    
                elif result["action"] == "continue_next_frame":
                    # 收到有效响应，显示到UI
                    self._display_received_data(response, f"帧{frame_number}")
                    
                    # 处理响应数据（如温度和电机转速）
                    current_command = self.command_handler.get_command_state_info()["current_command"]
                    self._process_data_and_display_results(current_command, unescaped_response)
                    
                    # 当前帧完成，准备下一帧
                    return True, f"第 {frame_number} 帧处理完成"
                    
                elif result["action"] == "command_complete":
                    # 收到有效响应，显示到UI
                    self._display_received_data(response, f"帧{frame_number}")
                    
                    # 处理响应数据（如温度和电机转速）
                    current_command = self.command_handler.get_command_state_info()["current_command"]
                    self._process_data_and_display_results(current_command, unescaped_response)
                    
                    # 指令完成
                    return True, "指令完成"
                    
                elif result["validation_result"]:
                    # 收到有效响应，显示到UI
                    self._display_received_data(response, f"帧{frame_number}")
                    
                    # 处理响应数据（如温度和电机转速）
                    current_command = self.command_handler.get_command_state_info()["current_command"]
                    self._process_data_and_display_results(current_command, unescaped_response)
                    
                    # 验证成功但没有后续动作（不需要响应帧的指令）
                    return True, f"第 {frame_number} 帧验证成功"
                    
                else:
                    # 收到无效响应，也显示到UI以便调试
                    self._display_received_data(response, f"帧{frame_number}")
                    # 验证失败
                    self.logger_manager.log_error(f"帧 {frame_number} 验证失败: {result['message']}")
                    return False, f"第 {frame_number} 帧验证失败: {result['message']}"
                    
            self.logger_manager.log_error(f"帧 {frame_number} 处理超过最大迭代次数 {max_iterations}")
            return False, f"第 {frame_number} 帧处理超过最大迭代次数 {max_iterations}"
            
        except Exception as e:
            self.logger_manager.log_error(f"处理第 {frame_number} 帧异常: {str(e)}")
            return False, f"处理第 {frame_number} 帧异常: {str(e)}"
            
    def _prepare_command_params(self, command_name: str, content: str, spec: str, file_path: str, file_type: str) -> dict:
        """准备指令参数"""
        params = {}
        
        if command_name == "写RCU序列号":
            params["internal_sn"] = self.internal_sn
        elif command_name == "写电子标签":
            params["serial_number"] = content or self.barcode
        elif command_name in ["写模块文件", "写天线文件", "写合路器信息文件"]:
            params["file_path"] = file_path
            # 为写合路器信息文件指令添加文件类型参数
            if command_name == "写合路器信息文件":
                params["file_type"] = file_type  # 直接使用传入的 file_type 参数
        elif command_name in ["回读电子标签", "回读模块文件", "回读天线文件"]:
            params["frame_num"] = "0"  # 默认从第0帧开始
        elif command_name == "回读合路器信息文件":
            # 新增：回读合路器信息文件指令参数
            params["file_type"] = file_type or "模块信息文件"  # 文件类型（必需）
            params["frame_num"] = 0  # 默认从第0帧开始
            if file_path:  # 如果提供了文件路径，用于数据验证
                params["file_path"] = file_path
        elif command_name == "对设备分配地址并建链":
            params["internal_sn"] = self.internal_sn
        elif command_name == "设置天线倾角":
            # 尝试将内容转换为整数角度值
            try:
                params["angle"] = int(content) if content else 0
            except ValueError:
                self.logger_manager.log_error(f"无效的倾角值: {content}，使用默认值0")
                params["angle"] = 0
        elif command_name == "读取电机转速":
            params["content"] = content  # 添加content参数传递
        elif command_name in ["读取电流值", "读取电压值"]:
            # 电源指令参数处理
            params["port"] = content or "1"  # 默认端口1
        elif command_name == "查询软硬件版本信息":
            params["spec"] = spec
            params["content"] = content
        return params
        
    def _display_received_data(self, data: bytes, prefix: str = ""):
        """显示接收到的数据"""
        if self.ui_manager:
            hex_str = data.hex().upper()
            ascii_str = data.replace(b'\r\n', b'').decode('ascii', errors='ignore')
            formatted_hex = ' '.join([hex_str[i:i+2] for i in range(0, len(hex_str), 2)])

            if prefix:
                # 🔧 修复：电压值、电流值、监控数据和软硬件版本信息采用ASCII码显示
                if prefix in ["电源指令响应", "校准读取电压值", "校准读取电流值", "校准监控", "软硬件版本信息"]:
                    display_text = f"{prefix}: {ascii_str}" if ascii_str.strip() else f"{prefix}: {formatted_hex}"
                else:
                    display_text = f"{prefix}: {formatted_hex}"
            else:
                display_text = formatted_hex

            self.ui_append_back_data.emit(self.current_row, display_text)
            
    def get_statistics(self) -> dict:
        """获取统计信息"""
        serial_stats = self.serial_manager.get_statistics()
        command_state = self.command_handler.get_command_state_info()
        
        return {
            "serial_stats": serial_stats,
            "command_state": command_state,
            "current_test": {
                "total_cases": len(self.test_cases),
                "current_row": self.current_row,
                "is_running": self.is_running
            }
        }
    
    def set_test_cases(self, test_cases: List[dict]):
        """设置测试用例 - 保持向后兼容"""
        self.test_cases = test_cases
        self.total_rows = len(test_cases)
        
    def set_device_info(self, barcode: str, internal_sn: str):
        """设置设备信息 - 保持向后兼容"""
        self.barcode = barcode
        self.internal_sn = internal_sn
        
    def next_step(self):
        """进入下一步 - 手动模式下使用"""
        if self.is_manual_mode and self.should_pause:
            self.should_pause = False 

    def _execute_async_antenna_calibration(self) -> Tuple[bool, str]:
        """
        执行异步天线校准序列

        新的天线校准流程：
        1. 发送天线校准命令（天线开始校准，后台进行）
        2. 等待3秒让天线校准稳定
        3. 在32秒校准期间，并行读取电压、电流和电机转速的最大值
        4. 校准完成后返回结果
        """
        try:
            self.logger_manager.log_info("开始执行异步天线校准序列")

            # 第1步：发送天线校准命令
            success, message = self._send_antenna_calibration_command()
            if not success:
                return False, f"发送天线校准命令失败: {message}"

            # 第2步：等待3秒让天线校准稳定
            self.logger_manager.log_info("等待3秒让天线校准稳定...")
            time.sleep(3.0)

            # 第3步：在校准期间并行读取电压、电流和电机转速
            calibration_success, calibration_message = self._execute_calibration_monitoring()
            if not calibration_success:
                return False, f"校准期间监控失败: {calibration_message}"

            # 第4步：天线校准指令结束后，发送响应帧
            self.logger_manager.log_info("天线校准监控完成，发送最终响应帧")
            try:
                # 发送天线校准完成的响应帧
                response_frame = self.command_handler.get_response_frame()
                if not self.serial_manager.send_command(
                    response_frame, "天线校准完成响应帧", is_response=True
                ):
                    self.logger_manager.log_warning("发送天线校准完成响应帧失败，但不影响整体流程")
                else:
                    # 等待响应帧回传
                    echo_response = self.serial_manager.receive_single_frame(timeout=1.0)
                    if echo_response == bytes.fromhex("7E 01 73 83 57 7E"):
                        self.logger_manager.log_info("天线校准完成响应帧回传验证成功")
                    else:
                        self.logger_manager.log_warning("天线校准完成响应帧回传验证失败，但不影响整体流程")
            except Exception as e:
                self.logger_manager.log_warning(f"发送天线校准完成响应帧异常: {str(e)}，但不影响整体流程")

            self.logger_manager.log_info("异步天线校准序列执行完成")
            return True, "异步天线校准序列执行成功"

        except Exception as e:
            return False, f"执行异步天线校准序列异常: {str(e)}"

    def _format_calibration_result(self, command_name: str, calibration_result: dict) -> str:
        """格式化校准读取结果为可读字符串"""
        try:
            # 检查是否是交替读取模式的结果
            if "voltage" in calibration_result and "current" in calibration_result:
                # 交替读取模式
                voltage_data = calibration_result["voltage"]
                current_data = calibration_result["current"]

                result_parts = ["校准交替读取完成:"]
                result_parts.append(f"电压: 最大值={voltage_data['max_value']}V, 读取次数={voltage_data['total_readings']}")
                result_parts.append(f"电流: 最大值={current_data['max_value']}A, 读取次数={current_data['total_readings']}")

                return " | ".join(result_parts)
            else:
                # 单一类型读取模式
                max_value = calibration_result.get("max_value", 0)
                total_readings = calibration_result.get("total_readings", 0)
                successful_reads = calibration_result.get("successful_reads", 0)
                total_attempts = calibration_result.get("total_attempts", 0)

                unit = "V" if command_name == "读取电压值" else "A"

                result_parts = [f"校准{command_name}完成:"]
                result_parts.append(f"最大值={max_value}{unit}")
                result_parts.append(f"有效读取={total_readings}次")

                if total_attempts and total_attempts != successful_reads:
                    result_parts.append(f"尝试={total_attempts}次")
                    result_parts.append(f"成功={successful_reads}次")

                # 添加电机转速读取信息（如果有）
                if "motor_readings" in calibration_result:
                    motor_data = calibration_result["motor_readings"]
                    result_parts.append(f"并行电机转速读取={motor_data['successful_reads']}次")

                return " | ".join(result_parts)

        except Exception as e:
            # 如果格式化失败，返回基本信息
            return f"校准{command_name}完成，详细信息解析失败: {str(e)}"
    
    def _send_antenna_calibration_command(self) -> Tuple[bool, str]:
        """发送天线校准命令（不等待完成）"""
        try:
            # 准备天线校准命令参数
            params = self._prepare_command_params("天线校准命令", "", "", "")
            
            # 生成指令
            command_bytes = self.command_handler.generate_command("天线校准命令", **params)
            if command_bytes is None:
                return False, "无法生成天线校准指令"
            
            # 转义并发送指令
            escaped_command = self.command_handler.escape_command(command_bytes)
            
            if not self.serial_manager.send_command(escaped_command, "天线校准命令"):
                return False, "发送天线校准指令失败"
            
            # 接收响应（可能是正常响应或中间帧前置）
            response = self.serial_manager.receive_single_frame(timeout=3.0)
            if response is None:
                return False, "接收天线校准响应超时"
            
            # 显示接收到的数据
            self._display_received_data(response, "天线校准")
            
            # 反转义响应
            unescaped_response = self.command_handler.unescape_response(response)
            
            # 验证响应（正常响应或中间帧前置都算成功）
            if not self.command_handler.validate_response("天线校准命令", unescaped_response, **params):
                return False, "天线校准响应验证失败"
            
            # 发送响应帧
            if self.command_handler.needs_response_frame("天线校准命令"):
                response_frame = self.command_handler.get_response_frame()
                if not self.serial_manager.send_command(
                    response_frame, "RESPONSE_FRAME", is_response=True
                ):
                    return False, "发送天线校准响应帧失败"
                    
                # 等待响应帧回传
                echo_response = self.serial_manager.receive_single_frame(timeout=1.0)
                if echo_response != bytes.fromhex("7E 01 73 83 57 7E"):
                    return False, "天线校准响应帧回传验证失败"
            
            self.logger_manager.log_info("天线校准命令发送成功，天线开始后台校准")
            return True, "天线校准命令发送成功"
            
        except Exception as e:
            return False, f"发送天线校准命令异常: {str(e)}"
    
    def _execute_calibration_monitoring(self) -> Tuple[bool, str]:
        """
        在天线校准期间智能监控电压、电流和电机转速

        实现策略：
        1. 启动电源处理器的校准状态
        2. 分析后续测试用例，确定需要监控的项目
        3. 在32秒校准期间，只读取需要的数据
        4. 记录所有读取的最大值
        5. 结束时停止校准状态
        """
        try:
            self.logger_manager.log_info("开始校准期间的智能监控")

            # 🔧 修复1：启动电源处理器的校准状态
            self.command_handler.power_handler.start_calibration()
            self.logger_manager.log_info("已启动电源处理器校准状态")

            # 🔧 新增：分析后续测试用例，确定监控需求
            monitoring_needs = self._analyze_monitoring_needs()
            self.logger_manager.log_info(f"监控需求分析: {monitoring_needs}")

            # 如果没有需要监控的项目，只等待校准时间
            if not any(monitoring_needs.values()):
                self.logger_manager.log_info("无需监控电压电流电机转速，仅等待校准完成")
                time.sleep(32.0)  # 等待校准完成
                self.command_handler.power_handler.stop_calibration()
                return True, "天线校准完成（无监控项目）"

            # 初始化监控数据
            monitoring_data = {
                "voltage_readings": [],
                "current_readings": [],
                "motor_speed_readings": [],
                "max_voltage": 0.0,
                "max_current": 0.0,
                "max_motor_speed": 0,
                "total_attempts": 0,
                "successful_reads": 0,
                "monitoring_needs": monitoring_needs
            }

            # 获取电源接口和RCU接口的串口配置
            power_port = self.config_manager.get_port_by_name("电源AISGIN")
            rcu_port = self.config_manager.get_port_by_name("AISGIN")

            if not power_port:
                # 确保停止校准状态
                self.command_handler.power_handler.stop_calibration()
                return False, "未找到电源接口串口配置"
            if not rcu_port:
                # 确保停止校准状态
                self.command_handler.power_handler.stop_calibration()
                return False, "未找到RCU接口串口配置"

            self.logger_manager.log_info(f"GPD3303S电源设备: {power_port}, RCU产品: {rcu_port}")

            # 🔧 修复2：使用与电源处理器一致的校准时间（30秒）
            calibration_duration = 32.0  # 与电源处理器保持一致
            start_time = time.time()
            read_cycle = 0

            self.logger_manager.log_info(f"开始校准监控，持续时间: {calibration_duration}秒")

            while (time.time() - start_time) < calibration_duration:
                read_cycle += 1
                monitoring_data["total_attempts"] += 1

                try:
                    # 🔧 修改：根据监控需求有选择地读取数据

                    # 第1步：根据需求读取电压值
                    if monitoring_data["monitoring_needs"]["voltage"]:
                        if self._switch_to_port_safely(power_port, "GPD3303S电源设备"):
                            voltage_value = self._read_power_value("读取电压值", "1")
                            if voltage_value is not None:
                                monitoring_data["voltage_readings"].append(voltage_value)
                                if voltage_value > monitoring_data["max_voltage"]:
                                    monitoring_data["max_voltage"] = voltage_value
                                self.logger_manager.log_debug(f"校准监控第{read_cycle}次 - 电压: {voltage_value}V")
                        time.sleep(0.1)

                    # 第2步：根据需求读取电流值
                    if monitoring_data["monitoring_needs"]["current"]:
                        # 如果电压和电流都需要读取，复用电源接口连接
                        if not monitoring_data["monitoring_needs"]["voltage"]:
                            # 如果没有读取电压，需要切换到电源接口
                            if not self._switch_to_port_safely(power_port, "GPD3303S电源设备"):
                                continue

                        current_value = self._read_power_value("读取电流值", "1")
                        if current_value is not None:
                            monitoring_data["current_readings"].append(current_value)
                            if current_value > monitoring_data["max_current"]:
                                monitoring_data["max_current"] = current_value
                            self.logger_manager.log_debug(f"校准监控第{read_cycle}次 - 电流: {current_value}A")
                        time.sleep(0.1)

                    # 第3步：根据需求读取电机转速
                    if monitoring_data["monitoring_needs"]["motor_speed"]:
                        if self._switch_to_port_safely(rcu_port, "RCU产品"):
                            # 🔧 修复：传入正确的电机编号content参数
                            motor_content = monitoring_data["monitoring_needs"].get("motor_speed_content", "")
                            motor_speed = self._read_motor_speed_value(motor_content)
                            if motor_speed is not None:
                                monitoring_data["motor_speed_readings"].append(motor_speed)
                                if motor_speed > monitoring_data["max_motor_speed"]:
                                    monitoring_data["max_motor_speed"] = motor_speed
                                self.logger_manager.log_debug(f"校准监控第{read_cycle}次 - 电机转速: {motor_speed} (电机编号: {motor_content})")
                        time.sleep(0.1)

                    monitoring_data["successful_reads"] += 1

                    # 控制读取频率，避免过于频繁
                    time.sleep(0.1)

                except Exception as e:
                    self.logger_manager.log_warning(f"校准监控第{read_cycle}次读取异常: {str(e)}")
                    time.sleep(0.1)
                    continue

            # 🔧 修复3：停止电源处理器的校准状态
            self.command_handler.power_handler.stop_calibration()
            self.logger_manager.log_info("已停止电源处理器校准状态")

            # 生成监控结果报告
            result_message = self._format_monitoring_result(monitoring_data)
            self.logger_manager.log_info(result_message)

            # 显示监控结果到UI
            self._display_monitoring_result(monitoring_data)

            # 标记相关的测试用例为已执行
            self._mark_monitoring_commands_as_executed(monitoring_data)

            return True, result_message

        except Exception as e:
            # 🔧 修复4：异常时也要停止校准状态
            try:
                self.command_handler.power_handler.stop_calibration()
                self.logger_manager.log_info("异常处理：已停止电源处理器校准状态")
            except:
                pass
            return False, f"校准监控异常: {str(e)}"
    
    def _switch_to_port_safely(self, target_port: str, device_name: str) -> bool:
        """
        安全地切换到目标串口

        Args:
            target_port: 目标串口名称
            device_name: 设备名称（用于日志）

        Returns:
            bool: 是否切换成功
        """
        try:
            # 检查是否已经连接到目标串口
            if (self.current_connected_port == target_port and
                self.serial_manager.ser and
                self.serial_manager.ser.is_open):
                return True

            # 需要切换串口
            if self.current_connected_port:
                self.serial_manager.close_port()
                self.current_connected_port = None
                self.current_device_name = None

            # 连接到新串口
            if self.serial_manager.open_port(target_port):
                self.current_connected_port = target_port
                self.current_device_name = device_name
                self.logger_manager.log_debug(f"成功切换到{device_name}: {target_port}")
                return True
            else:
                self.logger_manager.log_warning(f"切换到{device_name}失败: {target_port}")
                return False

        except Exception as e:
            self.logger_manager.log_error(f"切换串口异常: {str(e)}")
            return False

    def _read_power_value(self, command_name: str, port: str) -> Optional[float]:
        """
        读取电源值（电压或电流）

        Args:
            command_name: 指令名称（"读取电压值" 或 "读取电流值"）
            port: 端口号

        Returns:
            float: 读取的值，失败返回None
        """
        try:
            # 生成电源指令
            if command_name == "读取电压值":
                cmd_bytes = self.command_handler.power_handler._generate_read_voltage_command(port)
            elif command_name == "读取电流值":
                cmd_bytes = self.command_handler.power_handler._generate_read_current_command(port)
            else:
                return None

            # 发送指令
            if not self.serial_manager.send_power_command(cmd_bytes, f"校准{command_name}"):
                return None

            # 接收响应（使用较短超时）
            response = self.serial_manager.receive_power_response(timeout=0.2)
            if not response:
                return None

            # 🔧 修复：显示ASCII格式的电源响应
            try:
                response_str = response.decode('ascii', errors='ignore').strip()
                self.logger_manager.log_debug(f"校准{command_name}响应: {response_str}")
                # 显示到UI（ASCII格式）
                self._display_received_data(response, f"校准{command_name}")
            except Exception as e:
                self.logger_manager.log_debug(f"显示电源响应异常: {str(e)}")

            # 解析响应
            parsed = self.command_handler.power_handler.parse_power_response(response, command_name)
            if parsed["success"] and parsed["parsed_data"]:
                if command_name == "读取电压值":
                    return parsed["parsed_data"].get("voltage", 0.0)
                else:
                    return parsed["parsed_data"].get("current", 0.0)

            return None

        except Exception as e:
            self.logger_manager.log_debug(f"读取{command_name}异常: {str(e)}")
            return None

    def _read_motor_speed_value(self, content: str = "") -> Optional[int]:
        """
        读取电机转速值（校准期间完整版本）

        正确处理中间帧前置的情况，确保能够获得准确的电机转速数据

        Args:
            content: 电机编号（从XML文件的content列获取）

        Returns:
            int: 电机转速值，失败返回None
        """
        try:
            # 🔧 修复：根据content参数生成电机转速读取指令
            params = {"content": content} if content else {}
            command_bytes = self.command_handler.generate_command("读取电机转速", **params)
            if command_bytes is None:
                return None

            # 🔧 修复：开始指令序列，设置正确的状态
            self.command_handler.start_command_sequence("读取电机转速", **params)

            # 转义并发送指令
            escaped_command = self.command_handler.escape_command(command_bytes)
            if not self.serial_manager.send_command(escaped_command, "校准电机转速"):
                return None

            # 🔧 修复：使用完整的响应处理逻辑，正确处理中间帧前置
            max_iterations = 5  # 校准期间限制迭代次数
            iteration = 0

            while iteration < max_iterations:
                iteration += 1
                self.logger_manager.log_debug(f"校准电机转速处理迭代 {iteration}/{max_iterations}")

                # 接收响应
                response = self.serial_manager.receive_single_frame(timeout=1.0)
                if response is None:
                    self.logger_manager.log_debug(f"校准电机转速第 {iteration} 次接收超时")
                    continue

                # 反转义响应
                unescaped_response = self.command_handler.unescape_response(response)

                # 🔧 修复：使用优先级处理逻辑，正确处理中间帧前置
                result = self.command_handler.process_response_with_priority(unescaped_response)

                if result["action"] == "send_middle_frame":
                    # 🔧 修复：收到中间帧前置，发送中间帧
                    middle_frame = result["next_command"]
                    self.logger_manager.log_debug("校准期间收到中间帧前置，发送中间帧")
                    if not self.serial_manager.send_command(middle_frame, "校准中间帧", is_response=True):
                        self.logger_manager.log_debug("校准期间发送中间帧失败")
                        return None
                    continue  # 继续等待最终响应

                elif result["action"] == "send_response_frame":
                    # 🔧 修复：收到有效响应，解析电机转速数据
                    motor_speed = self._parse_motor_speed_from_response(unescaped_response)
                    if motor_speed is not None:
                        # 发送响应帧
                        response_frame = result["next_command"]
                        if self.serial_manager.send_command(response_frame, "校准响应帧", is_response=True):
                            # 等待响应帧回传
                            echo_response = self.serial_manager.receive_single_frame(timeout=1.0)
                            if echo_response == bytes.fromhex("7E 01 73 83 57 7E"):
                                self.logger_manager.log_debug("校准期间响应帧回传验证成功")
                            else:
                                self.logger_manager.log_debug(f"校准期间响应帧回传验证失败: {echo_response.hex().upper() if echo_response else 'None'}")
                        # 🔧 修复：成功解析后重置指令状态
                        self.command_handler.reset_command_state()
                        return motor_speed
                    else:
                        self.logger_manager.log_debug("校准期间解析电机转速失败")
                        return None

                elif result["validation_result"]:
                    # 🔧 修复：验证成功，解析电机转速数据
                    motor_speed = self._parse_motor_speed_from_response(unescaped_response)
                    if motor_speed is not None:
                        self.logger_manager.log_debug(f"校准期间解析电机转速成功: {motor_speed}")
                        # 🔧 修复：成功解析后重置指令状态
                        self.command_handler.reset_command_state()
                        return motor_speed
                    else:
                        self.logger_manager.log_debug("校准期间解析电机转速失败")
                        # 🔧 修复：解析失败也要重置指令状态
                        self.command_handler.reset_command_state()
                        return None
                else:
                    # 验证失败，继续尝试
                    self.logger_manager.log_debug(f"校准期间电机转速响应验证失败: {result['message']}")
                    continue

            self.logger_manager.log_debug(f"校准期间电机转速处理超过最大迭代次数 {max_iterations}")
            # 🔧 修复：重置指令状态
            self.command_handler.reset_command_state()
            return None

        except Exception as e:
            self.logger_manager.log_debug(f"读取电机转速异常: {str(e)}")
            # 🔧 修复：异常时也要重置指令状态
            self.command_handler.reset_command_state()
            return None

    def _parse_motor_speed_from_response(self, response: bytes) -> Optional[int]:
        """
        从响应数据中解析电机转速

        Args:
            response: 反转义后的响应数据

        Returns:
            int: 电机转速值，失败返回None
        """
        try:
            if len(response) >= 13:
                motor_speed = response[10]  # 电机转速在索引10
                self.logger_manager.log_debug(f"解析电机转速: {motor_speed}")
                return motor_speed
            else:
                self.logger_manager.log_debug(f"电机转速响应长度不足: {len(response)}")
                return None
        except Exception as e:
            self.logger_manager.log_debug(f"解析电机转速异常: {str(e)}")
            return None

    def _analyze_monitoring_needs(self) -> dict:
        """
        分析天线校准命令后的3行测试用例，确定校准期间需要监控的项目

        根据需求：读取表格指令集中的单行指令时，若该行指令是天线校准命令指令，
        则查看后3行指令，看是否需要在校准期间读取电压值，电流值或电机转速。

        Returns:
            dict: 监控需求字典 {"voltage": bool, "current": bool, "motor_speed": bool}
        """
        try:
            monitoring_needs = {
                "voltage": False,
                "current": False,
                "motor_speed": False,
                "motor_speed_content": ""  # 🔧 新增：保存电机转速的content参数
            }

            # 只检查天线校准命令后的3行指令
            max_check_rows = min(self.current_row + 4, len(self.test_cases))  # 当前行+3行

            self.logger_manager.log_info(f"分析天线校准后3行指令 (第{self.current_row+2}行到第{max_check_rows}行)")

            for i in range(self.current_row + 1, max_check_rows):
                test_case = self.test_cases[i]
                command_name = test_case.get("名称", "")
                content = test_case.get("内容", "")

                # 检查是否有相关的读取指令
                if command_name == "读取电压值":
                    monitoring_needs["voltage"] = True
                    self.logger_manager.log_info(f"发现第{i+1}行需要在校准期间读取电压值")
                elif command_name == "读取电流值":
                    monitoring_needs["current"] = True
                    self.logger_manager.log_info(f"发现第{i+1}行需要在校准期间读取电流值")
                elif command_name == "读取电机转速":
                    monitoring_needs["motor_speed"] = True
                    monitoring_needs["motor_speed_content"] = content  # 🔧 保存电机编号
                    self.logger_manager.log_info(f"发现第{i+1}行需要在校准期间读取电机转速 (电机编号: {content})")

            # 记录分析结果
            needed_items = [k for k, v in monitoring_needs.items() if v]
            if needed_items:
                self.logger_manager.log_info(f"校准期间需要监控: {', '.join(needed_items)}")
            else:
                self.logger_manager.log_info("校准期间无需监控电压、电流或电机转速")

            return monitoring_needs

        except Exception as e:
            self.logger_manager.log_error(f"分析监控需求异常: {str(e)}")
            # 异常时返回全部监控（保守策略）
            return {"voltage": True, "current": True, "motor_speed": True}

    def _process_data_and_display_results(self, command_name: str, response: bytes):
        """
        处理温度和电机转速数据，将其转换为10进制并显示在结果区域
        同时对温度进行与0x19的差值比较
        """
        try:
            # 解析响应数据
            result_data = self.command_handler.parse_response_data(command_name, response)
            
            # 处理温度数据
            if command_name == "回读温度信息" and "temperature" in result_data:
                # 获取温度值（已转为10进制）
                temperature = result_data["temperature"]
                
                # 计算与0x19(25)的差值
                temp_diff = abs(temperature - 0x19)
                temp_status = "正常" if temp_diff <= 3 else "异常"
                
                # 构建显示消息
                message = f"温度值: {temperature}(0x{temperature:02X}), 与标准值(0x19)差距: {temp_diff}, 状态: {temp_status}"
                
                # 记录日志
                self.logger_manager.log_info(f"温度数据处理: {message}")
                
                # 更新UI显示
                self.ui_append_result_data.emit(-1, command_name, "成功", message)
            
            # 处理电机转速数据
            elif command_name == "读取电机转速" and "motor_speed" in result_data:
                # 获取电机转速值（已转为10进制）
                motor_speed = result_data["motor_speed"]
                
                # 构建显示消息
                message = f"电机转速: {motor_speed}(0x{motor_speed:02X})"
                
                # 记录日志
                self.logger_manager.log_info(f"电机转速数据处理: {message}")
                
                # 更新UI显示
                self.ui_append_result_data.emit(-1, command_name, "成功", message)
            
            # 处理查询软硬件版本信息数据
            elif command_name == "查询软硬件版本信息" and "ascii_data" in result_data:
                # 获取ASCII解码后的版本信息
                ascii_data = result_data["ascii_data"]
                raw_data = result_data.get("raw_data", "")
                
                # 构建显示消息
                message = f"软硬件版本信息: {ascii_data}"
                
                # 记录日志
                self.logger_manager.log_info(f"软硬件版本信息处理: {message}")
                
                # 更新UI显示 - 显示在接收区
                self._display_received_data(response, "软硬件版本信息")
                
                # 更新UI显示 - 显示在结果区
                self.ui_append_result_data.emit(self.current_row, command_name, "成功", message)
                
        except Exception as e:
            error_msg = f"处理数据结果失败: {str(e)}"
            self.logger_manager.log_error(error_msg)
            self.ui_append_result_data.emit(-1, command_name, "错误", error_msg)

    def _format_monitoring_result(self, monitoring_data: dict) -> str:
        """
        格式化监控结果为可读字符串

        Args:
            monitoring_data: 监控数据字典

        Returns:
            str: 格式化的结果字符串
        """
        try:
            voltage_count = len(monitoring_data["voltage_readings"])
            current_count = len(monitoring_data["current_readings"])
            motor_count = len(monitoring_data["motor_speed_readings"])

            result_parts = [
                "天线校准期间监控完成:",
                f"电压: 最大值={monitoring_data['max_voltage']}V, 读取次数={voltage_count}",
                f"电流: 最大值={monitoring_data['max_current']}A, 读取次数={current_count}",
                f"电机转速: 最大值={monitoring_data['max_motor_speed']}, 读取次数={motor_count}",
                f"总尝试次数={monitoring_data['total_attempts']}, 成功次数={monitoring_data['successful_reads']}"
            ]

            return " | ".join(result_parts)

        except Exception as e:
            return f"监控结果格式化失败: {str(e)}"

    def _display_monitoring_result(self, monitoring_data: dict):
        """
        显示监控结果到UI

        Args:
            monitoring_data: 监控数据字典
        """
        try:
            # 格式化结果消息
            result_message = self._format_monitoring_result(monitoring_data)

            # 显示到接收数据区域
            display_data = f"校准监控结果: {result_message}".encode('utf-8')
            self._display_received_data(display_data, "校准监控")

            # 显示到结果数据区域
            self.ui_append_result_data.emit(
                self.current_row,
                "天线校准监控",
                "通过",
                result_message
            )

        except Exception as e:
            self.logger_manager.log_error(f"显示监控结果失败: {str(e)}")

    def _mark_monitoring_commands_as_executed(self, monitoring_data: dict = None):
        """
        标记监控相关的指令为已执行状态
        只标记天线校准命令后3行中实际被监控的指令

        Args:
            monitoring_data: 监控数据，包含monitoring_needs信息
        """
        try:
            # 初始化已执行行集合
            if not hasattr(self, '_executed_rows'):
                self._executed_rows = set()

            # 🔧 修改：根据实际监控需求标记指令
            if monitoring_data and "monitoring_needs" in monitoring_data:
                monitoring_needs = monitoring_data["monitoring_needs"]

                # 构建需要标记的指令映射
                commands_to_check = {
                    "读取电压值": monitoring_needs.get("voltage", False),
                    "读取电流值": monitoring_needs.get("current", False),
                    "读取电机转速": monitoring_needs.get("motor_speed", False)
                }
            else:
                # 兼容旧逻辑：如果没有监控数据，标记所有相关指令
                commands_to_check = {
                    "读取电压值": True,
                    "读取电流值": True,
                    "读取电机转速": True
                }

            # 只检查天线校准命令后的3行指令
            max_check_rows = min(self.current_row + 4, len(self.test_cases))  # 当前行+3行

            self.logger_manager.log_info(f"标记天线校准后3行中的监控指令 (第{self.current_row+2}行到第{max_check_rows}行)")

            for i in range(self.current_row + 1, max_check_rows):
                if i >= len(self.test_cases):
                    break

                test_case = self.test_cases[i]
                command_name = test_case.get("名称", "")

                # 只标记实际监控的指令
                if command_name in commands_to_check and commands_to_check[command_name]:
                    # 标记为已执行
                    self._executed_rows.add(i)

                    # 更新UI显示
                    self.step_completed.emit(i, True, "已在天线校准时执行")
                    self.progress_updated.emit(i, "已执行")
                    self._set_row_color(i, "green")
                    self.ui_append_result_data.emit(i, command_name, "通过", "在天线校准时并发执行")

                    # 更新统计
                    self.passed_steps += 1

                    self.logger_manager.log_info(f"第 {i + 1} 行 ({command_name}) 已标记为执行完成")
                elif command_name in commands_to_check:
                    self.logger_manager.log_info(f"第 {i + 1} 行 ({command_name}) 未在校准时监控，保持未执行状态")

        except Exception as e:
            self.logger_manager.log_error(f"标记监控指令执行状态失败: {str(e)}")

    def _mark_row_as_executed(self, row_index: int):
        """标记指定行为已执行状态"""
        try:
            # 初始化已执行行集合
            if not hasattr(self, '_executed_rows'):
                self._executed_rows = set()

            # 添加到已执行集合
            self._executed_rows.add(row_index)

            # 更新UI显示
            self.step_completed.emit(row_index, True, "已在天线校准时执行")
            self.progress_updated.emit(row_index, "已执行")
            self._set_row_color(row_index, "green")
            self.ui_append_result_data.emit(row_index, "读取电机转速", "通过", "在天线校准时并发执行")

            # 更新统计
            self.passed_steps += 1

            self.logger_manager.log_info(f"第 {row_index + 1} 行已标记为执行完成")

        except Exception as e:
            self.logger_manager.log_error(f"标记行 {row_index + 1} 执行状态失败: {str(e)}")

    def _is_row_executed(self, row_index: int) -> bool:
        """检查指定行是否已被执行"""
        try:
            if hasattr(self, '_executed_rows'):
                return row_index in self._executed_rows
            else:
                self._executed_rows = set()
                return False
        except:
            return False